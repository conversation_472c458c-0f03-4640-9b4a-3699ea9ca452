# ImgProcTools

一个开源的图像处理和分析工具库，提供高效的图像增强、分析和处理算法。

## 项目概述

这是一个前后端分离的图像处理工具项目，采用现代化的技术栈构建：

- **前端**：Vue.js 3 + Vite + TypeScript
- **后端**：Node.js + Express.js + TypeScript
- **架构**：前后端分离，RESTful API

## 快速开始

### 环境要求

- Node.js >= 20.x
- npm >= 8.x

### 安装与运行

1. **克隆项目**
   ```bash
   git clone https://gitee.com/web-accelerator/img-proc-tools.git
   cd img-proc-tools
   ```

2. **安装依赖**
   ```bash
   npm run install:all
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **访问应用**
   - 前端：http://localhost:5183
   - 后端 API：http://localhost:3011

### 项目结构

```
img-proc-tools/
├── client/          # Vue.js 前端应用
├── server/          # Express.js 后端 API
├── package.json     # 项目配置和脚本
└── DEVELOPMENT.md   # 详细开发指南
```

## 可用脚本

### 开发命令
- `npm run dev` - 同时启动前后端开发服务器
- `npm run client:dev` - 仅启动前端开发服务器
- `npm run server:dev` - 仅启动后端开发服务器

### 构建命令
- `npm run build` - 同时构建前后端生产版本
- `npm run build:client` - 仅构建前端生产版本
- `npm run build:server` - 仅构建后端生产版本

### 清理命令
- `npm run clean` - 清理前后端构建产物
- `npm run clean:client` - 清理前端构建产物
- `npm run clean:server` - 清理后端构建产物

### 其他命令
- `npm run install:all` - 安装所有依赖

## 技术特性

- ✅ **现代化技术栈**：Vue 3 + Vite + Express
- ✅ **全栈 TypeScript**：前后端都使用 TypeScript，类型安全的开发体验
- ✅ **热重载开发**：前后端都支持热重载
- ✅ **代码质量工具**：ESLint + Prettier
- ✅ **测试框架**：Vitest + Playwright
- ✅ **前后端分离**：独立开发和部署

## 开发指南

详细的开发指南请参考 [DEVELOPMENT.md](./DEVELOPMENT.md)

## API 文档

### 基础端点
- `GET /` - 服务器信息
- `GET /api/health` - 健康检查

### 图像处理端点
- `GET /api/images` - 获取图像列表
- `POST /api/images/upload` - 上传图像

## 参与贡献

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 [木兰宽松许可证，第2版](./LICENSE) 开源协议。
