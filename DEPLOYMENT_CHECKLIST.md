# 图像处理工具部署检查清单

## 部署前准备

### 服务器要求
- [ ] Ubuntu 20.04+ / CentOS 7+ / Debian 10+
- [ ] 最低 2GB 内存，推荐 4GB+
- [ ] 最低 20GB 存储，推荐 50GB+
- [ ] 公网IP，开放 80、443、22 端口
- [ ] 非 root 用户，具有 sudo 权限

### 域名和SSL (可选)
- [ ] 域名已解析到服务器IP
- [ ] SSL证书已准备 (Let's Encrypt 或其他)

## 快速部署方案

### 方案1: 本地打包 + 服务器部署 (推荐)
```bash
# 本地构建并部署到服务器 (一键完成)
./scripts/local-to-server.sh your-server.com

# 或者分步执行:
# 1. 本地构建
./scripts/local-build.sh

# 2. 上传到服务器
scp img-proc-tools-*.tar.gz user@server:/tmp/

# 3. 服务器部署
ssh user@server
./scripts/server-deploy.sh /tmp/img-proc-tools-*.tar.gz
```

### 方案2: 服务器端构建部署
```bash
# 下载部署脚本
wget https://raw.githubusercontent.com/your-repo/img-proc-tools/main/scripts/one-click-deploy.sh

# 添加执行权限
chmod +x one-click-deploy.sh

# 执行部署 (替换为你的域名)
./one-click-deploy.sh your-domain.com
```

### 部署验证
- [ ] 后端API响应: `curl http://localhost:3011`
- [ ] 前端页面访问: `curl http://localhost`
- [ ] PM2服务运行: `pm2 status`
- [ ] Nginx服务运行: `sudo systemctl status nginx`

## 手动部署步骤

### 1. 环境安装
- [ ] 安装 Node.js 18+
- [ ] 安装 PM2: `sudo npm install -g pm2`
- [ ] 安装 Nginx: `sudo apt install nginx`
- [ ] 安装 Git: `sudo apt install git`

### 2. 项目部署
- [ ] 克隆代码: `git clone https://gitee.com/web-accelerator/img-proc-tools.git`
- [ ] 安装依赖: `npm run install:all`
- [ ] 构建项目: `npm run build`

### 3. 后端配置
- [ ] 创建环境变量文件: `server/.env`
- [ ] 创建必要目录: `mkdir -p server/{uploads,results,logs}`
- [ ] 配置PM2: `ecosystem.config.js`
- [ ] 启动服务: `pm2 start ecosystem.config.js`

### 4. 前端配置
- [ ] 验证构建文件: `ls client/dist/`
- [ ] 配置Nginx指向: `/var/www/img-proc-tools/client/dist`

### 5. Nginx配置
- [ ] 创建站点配置: `/etc/nginx/sites-available/img-proc-tools`
- [ ] 启用站点: `sudo ln -s /etc/nginx/sites-available/img-proc-tools /etc/nginx/sites-enabled/`
- [ ] 测试配置: `sudo nginx -t`
- [ ] 重载配置: `sudo systemctl reload nginx`

## 部署后验证

### 功能测试
- [ ] 访问首页正常显示
- [ ] 上传图片功能正常
- [ ] API接口响应正常
- [ ] 文件下载功能正常

### 性能测试
- [ ] 页面加载速度 < 3秒
- [ ] API响应时间 < 1秒
- [ ] 文件上传速度正常
- [ ] 内存使用 < 80%

### 安全检查
- [ ] 防火墙配置正确
- [ ] 敏感文件权限设置
- [ ] SSL证书配置 (如果使用)
- [ ] 安全头设置正确

## 监控和维护

### 日志监控
- [ ] 应用日志: `pm2 logs img-proc-server`
- [ ] Nginx访问日志: `/var/log/nginx/img-proc-tools.access.log`
- [ ] Nginx错误日志: `/var/log/nginx/img-proc-tools.error.log`
- [ ] 系统日志: `journalctl -u nginx`

### 性能监控
- [ ] CPU使用率: `top`
- [ ] 内存使用: `free -h`
- [ ] 磁盘空间: `df -h`
- [ ] 进程状态: `pm2 monit`

### 备份策略
- [ ] 代码备份: Git仓库
- [ ] 数据备份: 上传文件和结果文件
- [ ] 配置备份: Nginx和PM2配置
- [ ] 定期备份计划

## 常见问题排查

### 服务无法启动
- [ ] 检查端口占用: `sudo netstat -tlnp | grep :3011`
- [ ] 检查PM2状态: `pm2 status`
- [ ] 查看错误日志: `pm2 logs img-proc-server`
- [ ] 检查环境变量: `cat server/.env`

### 前端无法访问
- [ ] 检查Nginx状态: `sudo systemctl status nginx`
- [ ] 检查配置语法: `sudo nginx -t`
- [ ] 查看错误日志: `sudo tail -f /var/log/nginx/error.log`
- [ ] 检查文件权限: `ls -la client/dist/`

### API接口错误
- [ ] 检查后端服务: `curl http://localhost:3011`
- [ ] 检查代理配置: Nginx location /api/
- [ ] 查看应用日志: `pm2 logs img-proc-server`
- [ ] 检查防火墙: `sudo ufw status`

### 文件上传失败
- [ ] 检查文件大小限制: Nginx `client_max_body_size`
- [ ] 检查目录权限: `ls -la server/uploads/`
- [ ] 检查磁盘空间: `df -h`
- [ ] 查看上传日志: 应用和Nginx日志

## 更新部署

### 代码更新
```bash
# 进入项目目录
cd /var/www/img-proc-tools

# 拉取最新代码
git pull origin main

# 安装新依赖
npm install
cd client && npm install && cd ..
cd server && npm install && cd ..

# 重新构建
cd client && npm run build && cd ..
cd server && npm run build && cd ..

# 重启服务
pm2 restart img-proc-server

# 重载Nginx
sudo systemctl reload nginx
```

### 自动化更新
- [ ] 使用部署脚本: `./scripts/deploy.sh`
- [ ] 设置CI/CD流水线
- [ ] 配置Webhook自动部署

## 安全加固

### 系统安全
- [ ] 定期更新系统: `sudo apt update && sudo apt upgrade`
- [ ] 配置防火墙: `sudo ufw enable`
- [ ] 禁用root登录
- [ ] 使用SSH密钥认证

### 应用安全
- [ ] 定期更新依赖: `npm audit fix`
- [ ] 配置HTTPS
- [ ] 设置安全头
- [ ] 限制文件上传类型和大小

### 监控告警
- [ ] 设置服务监控
- [ ] 配置磁盘空间告警
- [ ] 监控应用性能
- [ ] 设置日志告警

## 性能优化

### 前端优化
- [ ] 启用Gzip压缩
- [ ] 配置静态文件缓存
- [ ] 使用CDN (可选)
- [ ] 图片压缩优化

### 后端优化
- [ ] 调整PM2实例数
- [ ] 配置内存限制
- [ ] 启用集群模式 (可选)
- [ ] 数据库连接池 (如果使用)

### 系统优化
- [ ] 调整文件描述符限制
- [ ] 优化内核参数
- [ ] 配置Swap分区
- [ ] SSD存储优化

---

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 参考故障排查章节
3. 提交Issue到项目仓库
4. 联系技术支持

**部署完成后，请保存此检查清单用于后续维护！**
