# 本地打包 + 云端部署方案

## 部署流程概述

本方案适用于在本地开发环境打包，然后上传到云端服务器部署的场景：

1. **本地环境**: 开发、构建、打包
2. **云端服务器**: 运行环境、PM2管理、Nginx服务

## 本地打包步骤

### 1. 本地环境准备

```bash
# 确保本地环境
node --version  # 需要 v16.0.0+
npm --version

# 进入项目目录
cd img-proc-tools
```

### 2. 安装依赖并构建

```bash
# 安装所有依赖
npm install
cd client && npm install && cd ..
cd server && npm install && cd ..

# 构建前端 (生成静态文件)
cd client
npm run build
# 构建产物在 client/dist/ 目录
cd ..

# 构建后端 (TypeScript编译)
cd server
npm run build
# 编译产物在 server/dist/ 目录
cd ..
```

### 3. 创建部署包

使用提供的脚本创建部署包：

```bash
# 使用构建脚本
./scripts/build-production.sh

# 或者手动创建部署包
mkdir -p deploy-package
cp -r client/dist deploy-package/frontend
mkdir -p deploy-package/backend
cp -r server/dist deploy-package/backend/
cp server/package.json deploy-package/backend/
cp -r server/src/config deploy-package/backend/src/ 2>/dev/null || true
```

### 4. 创建服务器配置文件

```bash
# 创建 PM2 配置
cat > deploy-package/ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'img-proc-server',
      script: './backend/dist/index.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3011
      },
      error_file: './backend/logs/err.log',
      out_file: './backend/logs/out.log',
      log_file: './backend/logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      autorestart: true
    }
  ]
};
EOF

# 创建环境变量模板
cat > deploy-package/backend/.env.example << EOF
PORT=3011
NODE_ENV=production
COMFYUI_API_URL=http://localhost:8188
COMFYUI_HOST=localhost
COMFYUI_PORT=8188
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
RESULTS_DIR=results
API_TIMEOUT=300000
EOF
```

### 5. 打包上传文件

```bash
# 创建压缩包
tar -czf img-proc-tools-$(date +%Y%m%d_%H%M%S).tar.gz -C deploy-package .

# 或者使用脚本生成的包
ls -la img-proc-tools-*.tar.gz
```

## 云端服务器部署

### 1. 服务器环境准备

```bash
# 安装基础环境 (仅需一次)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs nginx
sudo npm install -g pm2

# 创建项目目录
sudo mkdir -p /var/www/img-proc-tools
sudo chown -R $USER:$USER /var/www/img-proc-tools
```

### 2. 上传部署包

```bash
# 方式1: 使用 scp 上传
scp img-proc-tools-*.tar.gz user@your-server:/tmp/

# 方式2: 使用 rsync 上传
rsync -avz img-proc-tools-*.tar.gz user@your-server:/tmp/

# 方式3: 使用 SFTP 工具上传 (如 FileZilla)
```

### 3. 服务器端解压部署

```bash
# 登录服务器
ssh user@your-server

# 解压部署包
cd /var/www/img-proc-tools
tar -xzf /tmp/img-proc-tools-*.tar.gz

# 安装后端生产依赖
cd backend
npm ci --only=production

# 创建必要目录和配置
mkdir -p uploads results logs
cp .env.example .env
# 编辑 .env 文件配置实际参数
vim .env

cd ..
```

### 4. 启动服务

```bash
# 启动后端服务
pm2 start ecosystem.config.js

# 保存 PM2 配置
pm2 save

# 设置开机自启
pm2 startup
# 执行输出的 sudo 命令
```

### 5. 配置 Nginx

```bash
# 创建 Nginx 配置
sudo tee /etc/nginx/sites-available/img-proc-tools << EOF
server {
    listen 5183;
    server_name your-domain.com;
    
    client_max_body_size 10M;
    
    # 前端静态文件
    location / {
        root /var/www/img-proc-tools/frontend;
        try_files \$uri \$uri/ /index.html;
        
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理到后端
    location /api/ {
        proxy_pass http://localhost:3011;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # 文件服务
    location ~ ^/(uploads|results)/ {
        proxy_pass http://localhost:3011;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    access_log /var/log/nginx/img-proc-tools.access.log;
    error_log /var/log/nginx/img-proc-tools.error.log;
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/img-proc-tools /etc/nginx/sites-enabled/

# 测试并重载 Nginx
sudo nginx -t
sudo systemctl reload nginx
```

## 自动化部署脚本

### 本地构建脚本

创建 `scripts/local-build.sh`:

```bash
#!/bin/bash
set -e

echo "开始本地构建..."

# 清理旧的构建文件
rm -rf deploy-package img-proc-tools-*.tar.gz

# 安装依赖
npm install
cd client && npm install && cd ..
cd server && npm install && cd ..

# 构建项目
echo "构建前端..."
cd client && npm run build && cd ..

echo "构建后端..."
cd server && npm run build && cd ..

# 创建部署包
echo "创建部署包..."
mkdir -p deploy-package
cp -r client/dist deploy-package/frontend
mkdir -p deploy-package/backend
cp -r server/dist deploy-package/backend/
cp server/package.json deploy-package/backend/
cp server/package-lock.json deploy-package/backend/ 2>/dev/null || true

# 创建配置文件
cat > deploy-package/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'img-proc-server',
    script: './backend/dist/index.js',
    instances: 1,
    exec_mode: 'fork',
    env: { NODE_ENV: 'production', PORT: 3011 },
    error_file: './backend/logs/err.log',
    out_file: './backend/logs/out.log',
    max_memory_restart: '1G',
    autorestart: true
  }]
};
EOF

cat > deploy-package/backend/.env.example << 'EOF'
PORT=3011
NODE_ENV=production
COMFYUI_API_URL=http://localhost:8188
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
RESULTS_DIR=results
API_TIMEOUT=300000
EOF

# 打包
PACKAGE_NAME="img-proc-tools-$(date +%Y%m%d_%H%M%S).tar.gz"
tar -czf $PACKAGE_NAME -C deploy-package .

echo "构建完成！"
echo "部署包: $PACKAGE_NAME"
echo "大小: $(du -sh $PACKAGE_NAME | cut -f1)"
```

### 服务器部署脚本

创建 `scripts/server-deploy.sh`:

```bash
#!/bin/bash
set -e

PACKAGE_FILE=$1
PROJECT_DIR="/var/www/img-proc-tools"

if [[ -z "$PACKAGE_FILE" ]]; then
    echo "用法: $0 <部署包文件>"
    exit 1
fi

echo "开始服务器部署..."

# 备份现有部署
if [[ -d "$PROJECT_DIR" ]]; then
    sudo cp -r $PROJECT_DIR $PROJECT_DIR.backup.$(date +%Y%m%d_%H%M%S)
fi

# 创建项目目录
sudo mkdir -p $PROJECT_DIR
sudo chown -R $USER:$USER $PROJECT_DIR

# 解压部署包
cd $PROJECT_DIR
tar -xzf $PACKAGE_FILE

# 安装后端依赖
cd backend
npm ci --only=production

# 创建必要目录
mkdir -p uploads results logs

# 配置环境变量
if [[ ! -f .env ]]; then
    cp .env.example .env
    echo "请编辑 .env 文件配置参数"
fi

cd ..

# 重启服务
pm2 delete img-proc-server 2>/dev/null || true
pm2 start ecosystem.config.js
pm2 save

echo "部署完成！"
echo "前端地址: http://localhost:5183"
echo "后端API: http://localhost:3011"
```

## 部署验证

```bash
# 检查服务状态
pm2 status

# 检查后端API
curl http://localhost:3011

# 检查前端页面
curl http://localhost:5183

# 查看日志
pm2 logs img-proc-server
```

## 更新部署流程

```bash
# 1. 本地构建新版本
./scripts/local-build.sh

# 2. 上传到服务器
scp img-proc-tools-*.tar.gz user@server:/tmp/

# 3. 服务器部署
ssh user@server
./scripts/server-deploy.sh /tmp/img-proc-tools-*.tar.gz
```

## 优势对比

### 本地打包优势
- ✅ 本地环境可控，构建稳定
- ✅ 减少服务器资源消耗
- ✅ 构建失败不影响线上服务
- ✅ 可以在多个服务器部署相同包
- ✅ 支持离线部署

### 注意事项
- 🔸 确保本地和服务器 Node.js 版本兼容
- 🔸 注意文件权限和路径问题
- 🔸 环境变量需要在服务器端配置
- 🔸 依赖包需要在服务器端安装

这种方式更适合生产环境部署，可以确保构建环境的一致性和部署的可靠性。
