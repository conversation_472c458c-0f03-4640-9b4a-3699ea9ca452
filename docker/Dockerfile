# 图像处理工具 Docker 镜像
# 多阶段构建，优化镜像大小

# 阶段1: 构建前端
FROM node:18-alpine AS frontend-builder

WORKDIR /app/client

# 复制前端依赖文件
COPY client/package*.json ./

# 安装前端依赖
RUN npm ci --only=production=false

# 复制前端源码
COPY client/ ./

# 构建前端
RUN npm run build

# 阶段2: 构建后端
FROM node:18-alpine AS backend-builder

WORKDIR /app/server

# 复制后端依赖文件
COPY server/package*.json ./

# 安装后端依赖
RUN npm ci --only=production=false

# 复制后端源码
COPY server/ ./

# 构建后端
RUN npm run build

# 阶段3: 生产环境镜像
FROM node:18-alpine AS production

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 复制后端构建产物
COPY --from=backend-builder /app/server/dist ./server/dist
COPY --from=backend-builder /app/server/package*.json ./server/

# 复制前端构建产物
COPY --from=frontend-builder /app/client/dist ./client/dist

# 复制配置文件
COPY server/src/config ./server/src/config

# 安装生产环境依赖
WORKDIR /app/server
RUN npm ci --only=production && npm cache clean --force

# 创建必要目录
RUN mkdir -p uploads results logs && \
    chown -R nodejs:nodejs /app

# 切换到非 root 用户
USER nodejs

# 暴露端口
EXPOSE 3011

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3011 || exit 1

# 启动应用
CMD ["dumb-init", "node", "dist/index.js"]
