version: '3.8'

services:
  # 图像处理工具应用
  img-proc-app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: img-proc-tools
    restart: unless-stopped
    ports:
      - "3011:3011"
    environment:
      - NODE_ENV=production
      - PORT=3011
      - COMFYUI_API_URL=http://comfyui:8188
      - COMFYUI_HOST=comfyui
      - COMFYUI_PORT=8188
      - MAX_FILE_SIZE=10485760
      - API_TIMEOUT=300000
    volumes:
      - uploads_data:/app/server/uploads
      - results_data:/app/server/results
      - logs_data:/app/server/logs
    networks:
      - img-proc-network
    depends_on:
      - nginx
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3011"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: img-proc-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - img-proc-network
    depends_on:
      - img-proc-app

  # ComfyUI 服务 (可选)
  comfyui:
    image: comfyui/comfyui:latest
    container_name: img-proc-comfyui
    restart: unless-stopped
    ports:
      - "8188:8188"
    volumes:
      - comfyui_models:/app/models
      - comfyui_output:/app/output
    networks:
      - img-proc-network
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Redis 缓存 (可选)
  redis:
    image: redis:alpine
    container_name: img-proc-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - img-proc-network
    command: redis-server --appendonly yes

volumes:
  uploads_data:
    driver: local
  results_data:
    driver: local
  logs_data:
    driver: local
  nginx_logs:
    driver: local
  comfyui_models:
    driver: local
  comfyui_output:
    driver: local
  redis_data:
    driver: local

networks:
  img-proc-network:
    driver: bridge
