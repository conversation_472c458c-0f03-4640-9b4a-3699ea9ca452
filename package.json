{"name": "img-proc-tools", "version": "1.0.0", "description": "An open-source repository for image processing and analysis, providing tools and algorithms to enhance, analyze, and manipulate images efficiently.", "main": "index.js", "scripts": {"dev": "concurrently \"npm run client:dev\" \"npm run server:dev\"", "build": "concurrently \"npm run client:build\" \"npm run server:build\"", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "clean": "concurrently \"npm run clean:client\" \"npm run clean:server\"", "clean:client": "cd client && rm -rf dist", "clean:server": "cd server && rm -rf dist", "client:dev": "cd client && npm run dev", "server:dev": "cd server && npm run dev", "client:build": "cd client && npm run build", "server:build": "cd server && npm run build", "server:start": "cd server && npm start", "install:all": "npm install && cd client && npm install && cd ../server && npm install", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://gitee.com/web-accelerator/img-proc-tools.git"}, "keywords": ["image-processing", "vue", "express", "nodejs", "frontend", "backend"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.2.0"}}