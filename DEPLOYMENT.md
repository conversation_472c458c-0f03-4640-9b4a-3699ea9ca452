# 图像处理工具 - 云端服务器部署文档

## 项目概述

本项目是一个前后端分离的图像处理工具，采用传统服务器部署方式：
- **前端**: Vue 3 + TypeScript + Vite (打包为静态文件)
- **后端**: Node.js + Express + TypeScript (PM2进程管理)
- **Web服务器**: Nginx (反向代理 + 静态文件服务)
- **进程管理**: PM2 (后端服务管理)
- **端口配置**: 前端 5183，后端 3011

## 服务器环境要求

### 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / Debian 10+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 20GB，推荐 50GB+
- **网络**: 公网IP，开放 5183、3011、22 端口

### 软件依赖
- **Node.js**: v18.0.0+
- **npm**: v8.0.0+
- **PM2**: 进程管理器
- **Nginx**: 反向代理和静态文件服务
- **Git**: 代码拉取

## 1. 服务器初始化

### 1.1 更新系统
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 1.2 安装基础软件
```bash
# Ubuntu/Debian
sudo apt install -y curl wget git vim unzip

# CentOS/RHEL
sudo yum install -y curl wget git vim unzip
```

### 1.3 安装 Node.js
```bash
# 使用 NodeSource 官方脚本安装 Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 1.4 安装 PM2
```bash
sudo npm install -g pm2
pm2 --version
```

### 1.5 安装 Nginx
```bash
# Ubuntu/Debian
sudo apt install -y nginx

# CentOS/RHEL
sudo yum install -y nginx

# 启动并设置开机自启
sudo systemctl start nginx
sudo systemctl enable nginx
```

## 2. 项目部署

### 2.1 创建部署目录
```bash
sudo mkdir -p /var/www/img-proc-tools
sudo chown -R $USER:$USER /var/www/img-proc-tools
cd /var/www/img-proc-tools
```

### 2.2 克隆项目代码
```bash
git clone https://gitee.com/web-accelerator/img-proc-tools.git .
```

### 2.3 安装依赖
```bash
# 安装根目录依赖
npm install

# 安装前端依赖
cd client
npm install
cd ..

# 安装后端依赖
cd server
npm install
cd ..
```

## 3. 项目构建

### 3.1 构建前端 (生成静态文件)
```bash
cd client
npm run build
# 构建完成后，静态文件位于 client/dist/ 目录
ls -la dist/  # 查看构建产物
cd ..
```

### 3.2 构建后端 (TypeScript编译)
```bash
cd server
npm run build
# 编译完成后，JavaScript文件位于 server/dist/ 目录
ls -la dist/  # 查看编译产物
cd ..
```

### 3.3 验证构建结果
```bash
# 检查前端构建产物
ls -la client/dist/
# 应该包含: index.html, assets/ 等文件

# 检查后端构建产物
ls -la server/dist/
# 应该包含: index.js, src/ 等文件
```

## 4. 环境配置

### 4.1 创建后端环境变量文件
```bash
cat > server/.env << EOF
# 服务器配置
PORT=3011
NODE_ENV=production

# ComfyUI 配置 (根据实际情况修改)
COMFYUI_API_URL=http://localhost:8188
COMFYUI_HOST=localhost
COMFYUI_PORT=8188

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
RESULTS_DIR=results

# 其他配置
API_TIMEOUT=300000
EOF
```

### 4.2 创建必要目录
```bash
cd server
mkdir -p uploads results logs
chmod 755 uploads results logs
cd ..
```

## 5. PM2 进程管理配置

### 5.1 创建 PM2 配置文件
```bash
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'img-proc-server',
      script: './server/dist/index.js',
      cwd: '/var/www/img-proc-tools',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3011
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3011
      },
      error_file: './server/logs/err.log',
      out_file: './server/logs/out.log',
      log_file: './server/logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads', 'results'],
      merge_logs: true,
      autorestart: true
    }
  ]
};
EOF
```

### 5.2 启动后端服务
```bash
# 启动应用
pm2 start ecosystem.config.js

# 查看服务状态
pm2 status

# 查看日志
pm2 logs img-proc-server

# 保存PM2配置
pm2 save

# 设置开机自启动
pm2 startup
# 执行输出的命令 (通常是 sudo 开头的命令)
```

### 5.3 PM2 常用命令
```bash
# 查看所有进程
pm2 list

# 重启服务
pm2 restart img-proc-server

# 停止服务
pm2 stop img-proc-server

# 删除服务
pm2 delete img-proc-server

# 查看实时日志
pm2 logs img-proc-server --lines 100

# 监控资源使用
pm2 monit

# 重载服务 (0秒停机)
pm2 reload img-proc-server
```

## 6. Nginx 配置

### 6.1 创建 Nginx 配置文件
```bash
sudo tee /etc/nginx/sites-available/img-proc-tools << EOF
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名或IP
    
    # 客户端最大上传文件大小
    client_max_body_size 10M;
    
    # 前端静态文件
    location / {
        root /var/www/img-proc-tools/client/dist;
        try_files \$uri \$uri/ /index.html;
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:3011;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # 文件上传和结果文件
    location /uploads/ {
        proxy_pass http://localhost:3011;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
    
    location /results/ {
        proxy_pass http://localhost:3011;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # 日志
    access_log /var/log/nginx/img-proc-tools.access.log;
    error_log /var/log/nginx/img-proc-tools.error.log;
}
EOF
```

### 6.2 启用站点配置
```bash
sudo ln -s /etc/nginx/sites-available/img-proc-tools /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 7. SSL 证书配置 (可选但推荐)

### 7.1 安装 Certbot
```bash
sudo apt install -y certbot python3-certbot-nginx
```

### 7.2 获取 SSL 证书
```bash
sudo certbot --nginx -d your-domain.com
```

## 8. 部署脚本

### 8.1 创建部署脚本
```bash
cat > deploy.sh << 'EOF'
#!/bin/bash

# 图像处理工具部署脚本
set -e

PROJECT_DIR="/var/www/img-proc-tools"
BACKUP_DIR="/var/backups/img-proc-tools"
DATE=$(date +%Y%m%d_%H%M%S)

echo "开始部署图像处理工具..."

# 创建备份
echo "创建备份..."
sudo mkdir -p $BACKUP_DIR
sudo cp -r $PROJECT_DIR $BACKUP_DIR/backup_$DATE

# 进入项目目录
cd $PROJECT_DIR

# 拉取最新代码
echo "拉取最新代码..."
git pull origin main

# 安装依赖
echo "安装依赖..."
npm install
cd client && npm install && cd ..
cd server && npm install && cd ..

# 构建项目
echo "构建前端..."
cd client && npm run build && cd ..

echo "构建后端..."
cd server && npm run build && cd ..

# 重启服务
echo "重启后端服务..."
pm2 restart img-proc-server

# 重载 Nginx
echo "重载 Nginx..."
sudo nginx -t && sudo systemctl reload nginx

echo "部署完成！"
echo "前端地址: http://your-domain.com"
echo "后端API: http://your-domain.com/api"

# 清理旧备份 (保留最近5个)
echo "清理旧备份..."
sudo find $BACKUP_DIR -name "backup_*" -type d | sort -r | tail -n +6 | sudo xargs rm -rf

echo "部署脚本执行完成！"
EOF

chmod +x deploy.sh
```

### 8.2 创建快速重启脚本
```bash
cat > restart.sh << 'EOF'
#!/bin/bash

echo "重启图像处理工具服务..."

# 重启后端
pm2 restart img-proc-server

# 重载 Nginx
sudo systemctl reload nginx

echo "服务重启完成！"
EOF

chmod +x restart.sh
```

## 9. 监控和日志

### 9.1 查看服务状态
```bash
# 查看 PM2 进程状态
pm2 status
pm2 logs img-proc-server

# 查看 Nginx 状态
sudo systemctl status nginx

# 查看系统资源
htop
df -h
```

### 9.2 日志文件位置
- **后端日志**: `/var/www/img-proc-tools/server/logs/`
- **Nginx 日志**: `/var/log/nginx/img-proc-tools.*.log`
- **PM2 日志**: `pm2 logs`

## 10. 维护命令

### 10.1 常用维护命令
```bash
# 查看服务状态
pm2 status

# 重启服务
pm2 restart img-proc-server

# 查看日志
pm2 logs img-proc-server --lines 100

# 监控资源使用
pm2 monit

# 更新代码并重新部署
./deploy.sh

# 仅重启服务
./restart.sh
```

### 10.2 故障排查
```bash
# 检查端口占用
sudo netstat -tlnp | grep :3011
sudo netstat -tlnp | grep :80

# 检查防火墙
sudo ufw status

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

## 11. 安全建议

1. **防火墙配置**
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
```

2. **定期更新**
```bash
sudo apt update && sudo apt upgrade -y
npm audit fix
```

3. **备份策略**
- 定期备份代码和数据
- 设置自动备份脚本
- 测试备份恢复流程

4. **监控告警**
- 设置服务监控
- 配置磁盘空间告警
- 监控应用性能

## 12. 性能优化

1. **Nginx 优化**
- 启用 gzip 压缩
- 配置静态文件缓存
- 调整 worker 进程数

2. **Node.js 优化**
- 调整 PM2 实例数
- 配置内存限制
- 启用集群模式

3. **系统优化**
- 调整文件描述符限制
- 优化内核参数
- 配置 swap 分区

---

## 快速部署命令总结

```bash
# 1. 安装基础环境
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs nginx
sudo npm install -g pm2

# 2. 创建项目目录
sudo mkdir -p /var/www/img-proc-tools
sudo chown -R $USER:$USER /var/www/img-proc-tools
cd /var/www/img-proc-tools

# 3. 克隆项目代码
git clone https://gitee.com/web-accelerator/img-proc-tools.git .

# 4. 安装依赖
npm install
cd client && npm install && cd ..
cd server && npm install && cd ..

# 5. 构建项目
cd client && npm run build && cd ..
cd server && npm run build && cd ..

# 6. 配置环境变量
cd server
cat > .env << EOF
PORT=3011
NODE_ENV=production
COMFYUI_API_URL=http://localhost:8188
MAX_FILE_SIZE=10485760
EOF
mkdir -p uploads results logs
cd ..

# 7. 配置PM2
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'img-proc-server',
    script: './server/dist/index.js',
    cwd: '/var/www/img-proc-tools',
    env: { NODE_ENV: 'production', PORT: 3011 },
    error_file: './server/logs/err.log',
    out_file: './server/logs/out.log',
    max_memory_restart: '1G'
  }]
};
EOF

# 8. 启动后端服务
pm2 start ecosystem.config.js
pm2 save
pm2 startup

# 9. 配置Nginx (需要手动创建配置文件)
# 参考上面的 Nginx 配置章节

# 10. 验证部署
curl http://localhost:3011  # 后端API
curl http://localhost       # 前端页面
```

## 部署架构说明

```
用户请求 → Nginx (80/443) → 静态文件 (前端)
                          → 反向代理 (/api) → PM2 (3011) → Node.js 后端
```

- **前端**: 构建为静态文件，由 Nginx 直接服务
- **后端**: 编译为 JavaScript，由 PM2 管理 Node.js 进程
- **文件上传**: 通过 Nginx 代理到后端处理
- **API请求**: 通过 Nginx 反向代理到后端

部署完成后，您的应用将在 `http://your-domain.com` 可访问！
