# 图像处理工具 - 部署文档

## 项目概述

前后端分离的图像处理工具，采用本地打包 + 云端部署方式：
- **前端**: Vue 3 + TypeScript + Vite (静态文件，端口 5183)
- **后端**: Node.js + Express + TypeScript (PM2管理，端口 3011)
- **Web服务器**: Nginx (反向代理 + 静态文件服务)

## 技术栈

### 前端
- Vue.js 3 + TypeScript + Vite
- Vue Router + Pinia + ESLint + Prettier

### 后端
- Node.js + TypeScript + Express.js 4.x
- CORS + dotenv + ts-node + nodemon

### 部署环境
- PM2 (进程管理) + Nginx (反向代理)

## 部署流程

### 1. 本地构建

```bash
# 进入项目目录
cd img-proc-tools

# 安装依赖并构建 (使用根目录的 npm run build 命令)
npm run install:all
npm run build

# 验证构建结果
ls -la client/dist/     # 前端静态文件
ls -la server/dist/     # 后端编译文件
```

### 2. 创建部署包

```bash
# 方式1: 使用现有脚本 (推荐)
./scripts/local-build.sh

# 方式2: 手动创建
mkdir -p deploy-package
cp -r client/dist deploy-package/frontend
mkdir -p deploy-package/backend
cp -r server/dist deploy-package/backend/
cp server/package.json deploy-package/backend/
tar -czf img-proc-tools-$(date +%Y%m%d_%H%M%S).tar.gz -C deploy-package .
```

### 3. 上传到服务器

```bash
scp img-proc-tools-*.tar.gz user@your-server:/tmp/
```

### 4. 服务器环境准备 (仅首次)

```bash
# 安装基础环境
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs nginx
sudo npm install -g pm2

# 创建项目目录
sudo mkdir -p /var/www/img-proc-tools
sudo chown -R $USER:$USER /var/www/img-proc-tools
```

### 5. 服务器部署

```bash
# 方式1: 使用现有脚本 (推荐)
./scripts/server-deploy.sh /tmp/img-proc-tools-*.tar.gz

# 方式2: 手动部署
cd /var/www/img-proc-tools
tar -xzf /tmp/img-proc-tools-*.tar.gz

# 安装后端依赖
cd backend
npm ci --only=production
mkdir -p uploads results logs

# 配置环境变量
cat > .env << EOF
PORT=3011
NODE_ENV=production
COMFYUI_API_URL=http://localhost:8188
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
RESULTS_DIR=results
API_TIMEOUT=300000
EOF

cd ..
```

### 6. 配置 PM2

```bash
# 创建 PM2 配置 (如果使用脚本会自动创建)
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'img-proc-server',
    script: './backend/dist/index.js',
    cwd: '/var/www/img-proc-tools',
    instances: 1,
    exec_mode: 'fork',
    env: { NODE_ENV: 'production', PORT: 3011 },
    error_file: './backend/logs/err.log',
    out_file: './backend/logs/out.log',
    max_memory_restart: '1G',
    autorestart: true
  }]
};
EOF

# 启动服务
pm2 start ecosystem.config.js
pm2 save
pm2 startup
# 执行输出的 sudo 命令设置开机自启
```

### 7. 配置 Nginx

```bash
# 创建 Nginx 配置
sudo tee /etc/nginx/sites-available/img-proc-tools << EOF
server {
    listen 5183;
    server_name your-domain.com;
    client_max_body_size 10M;

    # 前端静态文件
    location / {
        root /var/www/img-proc-tools/frontend;
        try_files \$uri \$uri/ /index.html;

        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API 代理到后端
    location /api/ {
        proxy_pass http://localhost:3011;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 文件服务
    location ~ ^/(uploads|results)/ {
        proxy_pass http://localhost:3011;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }

    access_log /var/log/nginx/img-proc-tools.access.log;
    error_log /var/log/nginx/img-proc-tools.error.log;
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/img-proc-tools /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 验证部署

```bash
# 检查服务状态
pm2 status

# 检查后端API
curl http://localhost:3011

# 检查前端页面
curl http://localhost:5183

# 查看日志
pm2 logs img-proc-server
```

## 管理命令

### PM2 管理
```bash
pm2 list                    # 查看所有进程
pm2 restart img-proc-server # 重启服务
pm2 stop img-proc-server    # 停止服务
pm2 logs img-proc-server    # 查看日志
pm2 monit                   # 监控资源
```

### 更新部署
```bash
# 1. 本地重新构建并上传新包
# 2. 服务器端执行:
cd /var/www/img-proc-tools
tar -xzf /tmp/img-proc-tools-new-*.tar.gz
pm2 restart img-proc-server
sudo systemctl reload nginx
```

## 故障排查

### 常见问题
1. **端口被占用**: `sudo netstat -tlnp | grep :3011`
2. **服务无法启动**: `pm2 logs img-proc-server`
3. **前端无法访问**: `sudo nginx -t && sudo tail -f /var/log/nginx/error.log`
4. **API接口错误**: `curl http://localhost:3011`

### 检查清单
- [ ] Node.js 18+ 已安装
- [ ] PM2 已安装: `sudo npm install -g pm2`
- [ ] Nginx 已安装并运行
- [ ] 防火墙开放 5183、3011 端口
- [ ] 后端API响应: `curl http://localhost:3011`
- [ ] 前端页面访问: `curl http://localhost:5183`
- [ ] PM2服务运行: `pm2 status`

## 部署架构

```
用户请求 → Nginx (5183) → 静态文件 (前端)
                        → 反向代理 (/api) → PM2 (3011) → Node.js 后端
```

## 访问地址

部署完成后：
- **前端页面**: `http://your-domain.com:5183`
- **后端API**: `http://your-domain.com:3011`

## 安全建议

```bash
# 配置防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 5183
sudo ufw allow 3011

# 定期更新
sudo apt update && sudo apt upgrade -y
npm audit fix
```

---

**注意**:
1. 项目中的 `scripts/local-build.sh` 和 `scripts/server-deploy.sh` 提供了自动化部署
2. 请根据实际情况修改域名、端口和配置参数
3. 首次部署建议先在测试环境验证
