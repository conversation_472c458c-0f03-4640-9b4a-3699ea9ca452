# 开发指南

## 项目结构

```
img-proc-tools/
├── client/                 # 前端 Vue.js 应用
│   ├── src/               # 源代码
│   ├── public/            # 静态资源
│   ├── package.json       # 前端依赖配置
│   └── vite.config.ts     # Vite 配置
├── server/                # 后端 Express.js + TypeScript 应用
│   ├── index.ts           # 服务器入口文件
│   ├── tsconfig.json      # TypeScript 配置
│   ├── package.json       # 后端依赖配置
│   ├── .env               # 环境变量配置
│   └── .env.example       # 环境变量示例
├── package.json           # 根目录项目配置
└── README.md              # 项目说明
```

## 技术栈

### 前端
- **Vue.js 3** - 渐进式 JavaScript 框架
- **Vite** - 现代化构建工具和开发服务器
- **TypeScript** - 类型安全的 JavaScript
- **Vue Router** - 官方路由管理器
- **Pinia** - 状态管理库
- **Vitest** - 单元测试框架
- **Playwright** - E2E 测试框架
- **ESLint + Prettier** - 代码质量和格式化

### 后端
- **Node.js** - JavaScript/TypeScript 运行环境
- **TypeScript** - 类型安全的 JavaScript 超集
- **Express.js 4.x** - Web 应用框架
- **CORS** - 跨域资源共享中间件
- **dotenv** - 环境变量管理
- **ts-node** - TypeScript 直接执行器
- **nodemon** - 开发时自动重启

## 快速开始

### 1. 安装依赖

```bash
# 安装所有依赖（根目录、前端、后端）
npm run install:all

# 或者分别安装
npm install                    # 根目录依赖
cd client && npm install       # 前端依赖
cd ../server && npm install    # 后端依赖
```

### 2. 启动开发服务器

#### 同时启动前后端（推荐）
```bash
npm run dev
```
- 前端：http://localhost:5183
- 后端：http://localhost:3011

#### 分别启动

**启动前端开发服务器：**
```bash
npm run client:dev
# 或者
cd client && npm run dev
```

**启动后端开发服务器：**
```bash
npm run server:dev
# 或者
cd server && npm run dev
```

### 3. 验证服务

**检查后端 API：**
```bash
curl http://localhost:3011/api/health
```

**访问前端应用：**
浏览器打开 http://localhost:5183

## 可用脚本

### 根目录脚本

#### 开发命令
- `npm run dev` - 同时启动前后端开发服务器
- `npm run client:dev` - 启动前端开发服务器
- `npm run server:dev` - 启动后端开发服务器

#### 构建命令
- `npm run build` - 同时构建前后端生产版本
- `npm run build:client` - 仅构建前端生产版本
- `npm run build:server` - 仅构建后端生产版本

#### 清理命令
- `npm run clean` - 清理前后端构建产物
- `npm run clean:client` - 清理前端构建产物
- `npm run clean:server` - 清理后端构建产物

#### 其他命令
- `npm run server:start` - 启动后端生产服务器
- `npm run install:all` - 安装所有依赖

### 前端脚本（client 目录）
- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run preview` - 预览生产构建
- `npm run test:unit` - 运行单元测试
- `npm run test:e2e` - 运行 E2E 测试
- `npm run lint` - 代码检查
- `npm run format` - 代码格式化

### 后端脚本（server 目录）
- `npm run dev` - 启动开发服务器（nodemon + ts-node）
- `npm run build` - 编译 TypeScript 到 JavaScript
- `npm run build:watch` - 监听模式编译 TypeScript
- `npm start` - 启动生产服务器（需要先运行 build）

## 环境配置

### 后端环境变量

复制 `server/.env.example` 到 `server/.env` 并根据需要修改：

```bash
cd server
cp .env.example .env
```

主要配置项：
- `PORT` - 服务器端口（默认：3000）
- `NODE_ENV` - 运行环境（development/production）
- `CORS_ORIGIN` - 允许的跨域来源（默认：http://localhost:5183）

## API 端点

### 基础端点
- `GET /` - 服务器信息
- `GET /api/health` - 健康检查

### 图像处理端点（示例）
- `GET /api/images` - 获取图像列表
- `POST /api/images/upload` - 上传图像

## 开发注意事项

1. **端口配置**：
   - 前端默认端口：5183
   - 后端默认端口：3011
   - 确保端口未被占用

2. **CORS 配置**：
   - 后端已配置允许前端跨域访问
   - 如需修改前端端口，请同步更新后端 CORS 配置

3. **热重载**：
   - 前端支持热重载（Vite）
   - 后端支持自动重启（nodemon）

4. **代码质量**：
   - 前端配置了 ESLint 和 Prettier
   - 后端使用 TypeScript 提供类型检查
   - 建议在提交前运行 `npm run lint` 和 `npm run format`

## 故障排除

### 常见问题

1. **端口被占用**：
   ```bash
   # 查找占用端口的进程
   lsof -i :3011  # 后端端口
   lsof -i :5183  # 前端端口

   # 终止进程
   kill -9 <PID>
   ```

2. **依赖安装失败**：
   ```bash
   # 清理缓存
   npm cache clean --force
   
   # 删除 node_modules 重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Express 版本兼容性**：
   - 项目使用 Express 4.x 以确保稳定性
   - 如遇到路由问题，请检查 Express 版本

## 生产部署

### 构建生产版本

1. **构建所有组件**：
   ```bash
   npm run build
   ```

2. **构建产物**：
   - 前端：`client/dist/` - 静态文件，可部署到 CDN 或静态服务器
   - 后端：`server/dist/` - 编译后的 JavaScript 文件

3. **部署前端**：
   ```bash
   # 前端构建产物在 client/dist 目录
   # 可以部署到 Nginx、Apache 或任何静态文件服务器
   ```

4. **部署后端**：
   ```bash
   # 复制必要文件到生产服务器
   cp -r server/dist server/package.json server/.env.example /path/to/production/

   # 在生产服务器上安装依赖（仅生产依赖）
   npm install --production

   # 启动服务器
   npm start
   ```

### 环境变量配置

生产环境需要配置以下环境变量：
- `NODE_ENV=production`
- `PORT=3011`（或其他端口）
- `CORS_ORIGIN=https://your-frontend-domain.com`

## 下一步

1. 根据项目需求添加具体的图像处理功能
2. 配置数据库连接（如需要）
3. 添加用户认证和授权
4. 实现文件上传和处理逻辑
5. 添加更多的 API 端点
6. 完善前端 UI 组件
7. 配置 CI/CD 流水线
8. 添加监控和日志系统
