<template>
  <AppLayout>
    <MainContent>
      <!-- 参数设置面板 -->
      <template #parameters>
        <div class="loop-params">
          <div class="param-group">
            <label class="param-label">
              <span class="required">*</span>
              上传图片
            </label>
            <div class="upload-area" @click="triggerFileInput" @drop="handleDrop" @dragover.prevent>
              <input
                ref="fileInput"
                type="file"
                accept="image/*"
                @change="handleFileSelect"
                style="display: none"
              />
              <div v-if="!selectedFile" class="upload-content">
                <div class="upload-icon">🔄</div>
                <p class="upload-text">上传需要制作循环的图片</p>
                <p class="upload-hint">支持JPG/PNG格式，建议正方形图片</p>
                <p class="upload-paste-hint">💡 支持拖拽上传和Ctrl+V粘贴</p>
              </div>
              <div v-else class="file-selected">
                <div v-if="filePreview" class="file-preview">
                  <img :src="filePreview" alt="预览图" class="preview-image" />
                  <button class="remove-file-overlay" @click.stop="removeFile" title="移除图片">✕</button>
                </div>
              </div>
            </div>
          </div>

          <div class="param-group">
            <label class="param-label">
              <span class="required">*</span>
              输出张数
            </label>
            <div class="number-control-group">
              <button
                type="button"
                class="number-btn decrease"
                @click="decreaseOutputCount"
                :disabled="params.outputCount <= 1"
              >
                -
              </button>
              <span class="number-display">{{ params.outputCount }}</span>
              <button
                type="button"
                class="number-btn increase"
                @click="increaseOutputCount"
                :disabled="params.outputCount >= 10"
              >
                +
              </button>
            </div>
          </div>



          <div class="action-section">
            <button
              class="process-btn"
              :disabled="!canProcess"
              @click="startProcess"
            >
              <span v-if="!processing">生成循环图</span>
              <span v-else>
                处理中... ({{ processingProgress.current }}/{{ processingProgress.total }})
              </span>
            </button>
          </div>
        </div>
      </template>

      <!-- 结果展示面板 -->
      <template #results>
        <div class="loop-results">
          <div v-if="!hasResults && !processing" class="tutorial-section">
            <div class="feature-intro">
              <h4>🔄 无缝循环图案生成</h4>
              <div class="feature-description">
                <p>将任意图片转换为可无缝拼接的循环图案，适用于：</p>
                <ul class="feature-list">
                  <li>🎨 纹理贴图制作</li>
                  <li>🖼️ 背景图案设计</li>
                  <li>👕 服装印花设计</li>
                  <li>🏠 装饰材料图案</li>
                </ul>
              </div>
            </div>
            
            <div class="example-showcase">
              <div class="example-item">
                <div class="example-placeholder original">原图</div>
                <span class="example-label">原图</span>
              </div>
              <div class="example-arrow">→</div>
              <div class="example-item">
                <div class="tiled-preview">
                  <div class="tile-placeholder">🔄</div>
                  <div class="tile-placeholder">🔄</div>
                  <div class="tile-placeholder">🔄</div>
                  <div class="tile-placeholder">🔄</div>
                </div>
                <span class="example-label">循环效果</span>
              </div>
            </div>
          </div>

          <!-- 对比展示区域 -->
          <div v-if="hasResults" class="comparison-wrapper">
            <!-- 原图区域 -->
            <div class="comparison-item">
              <div class="comparison-image-container original">
                <!-- 原图3x3平铺显示 -->
                <div class="tiled-preview-container">
                  <div class="tiled-grid-3x3">
                    <img
                      v-for="i in 9"
                      :key="i"
                      :src="filePreview"
                      alt="原图平铺效果"
                      class="tiled-tile"
                    />
                  </div>
                </div>
                <div class="image-overlay">
                  <div class="image-info">
                    <span class="image-type">原图平铺</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分隔线 -->
            <div class="comparison-divider">
              <div class="divider-line"></div>
              <div class="divider-icon">→</div>
              <div class="divider-line"></div>
            </div>

            <!-- 结果区域 -->
            <div class="comparison-item">
              <div class="comparison-image-container result">
                <!-- 3x3平铺显示 -->
                <div class="tiled-preview-container">
                  <div class="tiled-grid-3x3">
                    <img
                      v-for="i in 9"
                      :key="i"
                      :src="selectedResult?.url"
                      alt="平铺效果"
                      class="tiled-tile"
                    />
                  </div>
                </div>

                <div class="image-overlay">
                  <div class="image-info">
                    <span class="image-type">平铺预览</span>
                    <span v-if="results.length > 1" class="version-info">版本 {{ selectedResultIndex + 1 }}</span>
                  </div>
                  <div class="result-actions-overlay">
                    <button class="download-btn-modern" @click="downloadImage(selectedResult)">
                      <i class="download-icon">⬇</i>
                      下载
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>
      </template>

      <!-- 底部缩略图选择器 -->
      <template #bottom>
        <div v-if="results.length > 1" class="bottom-thumbnails">
          <div class="thumbnails-container">
            <div
              v-for="(result, index) in results"
              :key="index"
              class="thumbnail-item"
              :class="{ active: selectedResultIndex === index }"
              @click="selectResult(index)"
            >
              <img :src="result.url" :alt="`版本 ${index + 1}`" class="thumbnail-img" />
              <div class="thumbnail-label">{{ index + 1 }}</div>
            </div>
          </div>
        </div>
      </template>

      <!-- 操作按钮 -->
      <template #actions>
        <button v-if="hasResults" class="clear-btn" @click="clearResults">
          清空结果
        </button>
      </template>
    </MainContent>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import AppLayout from '@/components/layout/AppLayout.vue'
import MainContent from '@/components/layout/MainContent.vue'

// 参数设置
const params = ref({
  outputCount: 1
})

// 文件处理
const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(null)
const filePreview = ref<string>('')

// 处理状态
const processing = ref(false)
const currentStep = ref(0)
const resultImage = ref('')
const resultSize = ref('')
const results = ref<Array<{ url: string; name: string }>>([])
const processingProgress = ref({ current: 0, total: 0 })

// 选中的结果图片索引
const selectedResultIndex = ref(0)

// 界面状态
const tiledCols = ref(3)
const tiledRows = ref(3)

// 计算属性
const canProcess = computed(() => {
  return selectedFile.value !== null && !processing.value
})

// 获取当前选中的结果
const selectedResult = computed(() => {
  return results.value[selectedResultIndex.value] || results.value[0]
})

// 是否有结果
const hasResults = computed(() => {
  return results.value.length > 0
})



// 方法
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    const file = target.files[0]
    selectedFile.value = file
    createFilePreview(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    const file = event.dataTransfer.files[0]
    selectedFile.value = file
    createFilePreview(file)
  }
}

// 创建文件预览
const createFilePreview = (file: File) => {
  if (file.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      filePreview.value = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  filePreview.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 输出张数控制函数
const decreaseOutputCount = () => {
  if (params.value.outputCount > 1) {
    params.value.outputCount--
  }
}

const increaseOutputCount = () => {
  if (params.value.outputCount < 10) {
    params.value.outputCount++
  }
}

// 选择结果图片
const selectResult = (index: number) => {
  selectedResultIndex.value = index
}

// 下载图片
const downloadImage = async (result: { url: string; name: string }) => {
  try {
    // 获取图片数据
    const response = await fetch(result.url)
    const blob = await response.blob()

    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl

    // 设置文件名，确保有正确的扩展名
    const fileName = result.name || 'seamless-loop'
    const extension = result.url.includes('.png') ? '.png' : '.jpg'
    link.download = fileName.includes('.') ? fileName : `${fileName}${extension}`

    // 触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)

    console.log('图片下载成功:', link.download)
  } catch (error) {
    console.error('下载失败:', error)
    alert('下载失败，请稍后重试')
  }
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  // 检查是否在输入框中粘贴，如果是则不处理
  const target = event.target as HTMLElement
  if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
    return
  }

  const items = event.clipboardData?.items
  if (!items) return

  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    if (item.type.indexOf('image') !== -1) {
      const file = item.getAsFile()
      if (file) {
        selectedFile.value = file
        createFilePreview(file)

        // 可以添加成功提示
        console.log('图片粘贴成功:', file.name || '剪贴板图片')
        break
      }
    }
  }
}



const updateTiledRows = () => {
  tiledRows.value = tiledCols.value
}

const startProcess = async () => {
  if (!selectedFile.value) return

  processing.value = true
  results.value = [] // 清空之前的结果

  try {
    const outputCount = params.value.outputCount
    processingProgress.value = { current: 0, total: outputCount }
    console.log(`开始处理，将生成 ${outputCount} 张循环图案`)

    // 循环调用API，生成指定数量的图片
    for (let i = 0; i < outputCount; i++) {
      processingProgress.value.current = i + 1
      console.log(`正在生成第 ${i + 1}/${outputCount} 张循环图案...`)

      // 创建FormData对象
      const formData = new FormData()
      formData.append('file', selectedFile.value)
      formData.append('mode', 'squareCycleDiagram')

      // 每次调用生成1张图片
      formData.append('outputCount', '1')

      // 调用后端API
      const response = await fetch('/api/comfyui/generate', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success && result.images && result.images.length > 0) {
        // 处理成功，将结果添加到数组中
        const newImages = result.images.map((img: any, index: number) => ({
          url: img.url,
          name: img.filename || `seamless_loop_${results.value.length + index + 1}.png`
        }))

        results.value.push(...newImages)
        console.log(`第 ${i + 1} 张循环图案生成成功`)

        // 设置第一张图片为显示结果
        if (i === 0) {
          resultImage.value = newImages[0].url
          resultSize.value = '原尺寸'
          selectedResultIndex.value = 0
        }
      } else {
        throw new Error(result.error || `第 ${i + 1} 张循环图案处理失败，未返回有效结果`)
      }

      // 如果不是最后一次调用，添加短暂延迟避免服务器压力
      if (i < outputCount - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    console.log(`所有循环图案生成完成，共生成 ${results.value.length} 张图片`)
  } catch (error: any) {
    console.error('生成失败:', error)
    // 显示错误提示
    alert(`处理失败: ${error.message}`)
  } finally {
    processing.value = false
  }
}



const downloadTiled = () => {
  console.log('下载平铺效果图')
}

const clearResults = () => {
  resultImage.value = ''
  selectedFile.value = null
  filePreview.value = ''
  currentStep.value = 0
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 监听结果变化，重置选中索引
watch(results, (newResults) => {
  if (newResults.length > 0) {
    selectedResultIndex.value = 0
  }
}, { deep: true })

// 生命周期钩子 - 添加粘贴事件监听
onMounted(() => {
  document.addEventListener('paste', handlePaste)
})

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>

<style scoped>
/* 基础样式复用前面的组件样式 */
.loop-params {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.param-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 16px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.param-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-heading);
  display: flex;
  align-items: center;
  gap: 4px;
}

.required {
  color: #ef4444;
}

.upload-area {
  border: 2px dashed var(--color-border);
  border-radius: 6px;
  padding: 20px 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--color-background);
}

.upload-area:hover {
  border-color: #8b5cf6;
  background-color: #faf5ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 32px;
}

.upload-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-heading);
  margin: 0;
}

.upload-hint {
  font-size: 12px;
  color: var(--color-text);
  margin: 0;
}

.upload-paste-hint {
  font-size: 11px;
  color: #6b7280;
  margin: 4px 0 0 0;
  font-style: italic;
}

/* 文件选择后的样式 */
.file-selected {
  display: flex;
  flex-direction: column;
}

.file-preview {
  position: relative;
  display: flex;
  justify-content: center;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.remove-file-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  color: white;
  font-size: 14px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  opacity: 0.8;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-file-overlay:hover {
  opacity: 1;
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
}

.slider-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #8b5cf6;
  cursor: pointer;
}

.slider-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-heading);
  min-width: 40px;
}

.param-hint {
  font-size: 12px;
  color: var(--color-text);
  margin: 4px 0 0 0;
}

.select-input {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
}

/* 数字控制组件样式 */
.number-control-group {
  display: flex;
  align-items: center;
  gap: 0;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: hidden;
  background: var(--color-background);
  width: fit-content;
}

.number-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  color: var(--color-text);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.number-btn:hover:not(:disabled) {
  background: var(--color-background-soft);
  color: var(--color-heading);
}

.number-btn:active:not(:disabled) {
  background: var(--color-background-mute);
  transform: scale(0.95);
}

.number-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.number-display {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 32px;
  padding: 0 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-heading);
  background: var(--color-background-soft);
  border-left: 1px solid var(--color-border);
  border-right: 1px solid var(--color-border);
}

/* 对比展示样式 */
.comparison-wrapper {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 20px;
  align-items: center;
  padding: 24px;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comparison-divider {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-text-light);
}

.divider-line {
  width: 20px;
  height: 1px;
  background: var(--color-border);
}

.divider-icon {
  font-size: 20px;
  color: var(--color-primary);
  font-weight: bold;
}

.comparison-image-container {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.comparison-image-container:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.comparison-image-container.original {
  border: 3px solid #e5e7eb;
}

.comparison-image-container.result {
  border: 3px solid #10b981;
}

.comparison-img {
  width: 100%;
  height: auto;
  min-height: 250px;
  max-height: 450px;
  object-fit: contain;
  display: block;
  background: #f9fafb;
}

/* 3x3平铺样式 */
.tiled-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  min-height: 250px;
  max-height: 450px;
  overflow: hidden;
}

.tiled-grid-3x3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 0;
  width: 100%;
  height: 100%;
  max-width: 450px;
  max-height: 450px;
  aspect-ratio: 1;
}

.tiled-tile {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.7) 0%,
    transparent 30%,
    transparent 70%,
    rgba(0, 0, 0, 0.7) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 16px;
}

.comparison-image-container:hover .image-overlay {
  opacity: 1;
}

.image-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.image-type {
  color: white;
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.version-info {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.result-actions-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
}

.download-btn-modern {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  color: var(--color-heading);
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.download-btn-modern:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.download-icon {
  font-size: 16px;
}





@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 底部缩略图选择器样式 */
.bottom-thumbnails {
  padding: 16px 24px;
}

.thumbnails-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  overflow-x: auto;
  padding: 8px 0;
}

.thumbnail-item {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: #f9fafb;
}

.thumbnail-item:hover {
  border-color: #d1d5db;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.thumbnail-item.active {
  border-color: #6366f1;
  box-shadow: 0 0 0 1px #6366f1, 0 4px 12px rgba(99, 102, 241, 0.3);
}

.thumbnail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.thumbnail-item:hover .thumbnail-img {
  transform: scale(1.05);
}

.thumbnail-label {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 3px;
  min-width: 14px;
  text-align: center;
}

.thumbnail-item.active .thumbnail-label {
  background: #6366f1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comparison-wrapper {
    padding: 16px;
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .comparison-divider {
    transform: rotate(90deg);
  }

  .divider-icon {
    transform: rotate(90deg);
  }

  .comparison-img {
    max-height: 300px;
    min-height: 200px;
  }

  .tiled-preview-container {
    max-height: 300px;
    min-height: 200px;
  }

  .tiled-grid-3x3 {
    max-width: 300px;
    max-height: 300px;
  }

  .bottom-thumbnails {
    padding: 8px 0;
  }

  .thumbnails-container {
    gap: 8px;
  }

  .thumbnail-item {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .comparison-wrapper {
    padding: 12px;
  }

  .comparison-img {
    min-height: 150px;
    max-height: 250px;
  }

  .tiled-preview-container {
    max-height: 250px;
    min-height: 150px;
  }

  .tiled-grid-3x3 {
    max-width: 250px;
    max-height: 250px;
  }

  .bottom-thumbnails {
    padding: 8px 0;
  }

  .thumbnail-item {
    width: 50px;
    height: 50px;
  }

  .thumbnail-label {
    font-size: 9px;
    padding: 1px 3px;
  }
}

.action-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border);
}

.process-btn {
  width: 100%;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.process-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.process-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 结果展示样式 */
.loop-results {
  position: relative;
  height: 100%;
}

.tutorial-section {
  display: flex;
  flex-direction: column;
  gap: 32px;
  align-items: center;
}

.feature-intro {
  text-align: center;
  max-width: 500px;
}

.feature-intro h4 {
  font-size: 20px;
  margin-bottom: 16px;
  color: var(--color-heading);
}

.feature-description p {
  margin-bottom: 12px;
  color: var(--color-text);
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.feature-list li {
  padding: 4px 0;
  font-size: 14px;
  color: var(--color-text);
}

.example-showcase {
  display: flex;
  align-items: center;
  gap: 24px;
}

.example-item {
  text-align: center;
}

.example-img {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.tiled-preview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  width: 150px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.tile-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.example-placeholder {
  width: 150px;
  height: 150px;
  border-radius: 8px;
  border: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  color: white;
}

.example-placeholder.original {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.tile-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  font-size: 20px;
}

.example-label {
  display: block;
  margin-top: 8px;
  font-size: 14px;
  color: var(--color-text);
}

.example-arrow {
  font-size: 24px;
  color: #8b5cf6;
  font-weight: bold;
}

.results-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.result-tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border);
  margin-bottom: 20px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-btn.active {
  color: #8b5cf6;
  border-bottom-color: #8b5cf6;
}

.result-content {
  flex: 1;
  overflow-y: auto;
}

.single-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.result-image-container {
  position: relative;
  max-width: 400px;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  overflow: hidden;
}

.result-img {
  width: 100%;
  height: auto;
  display: block;
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(0,0,0,0.2) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,0,0,0.2) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

.result-info {
  text-align: center;
  font-size: 14px;
  color: var(--color-text);
}

.result-info p {
  margin: 4px 0;
}

.tiled-result {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tiled-container {
  display: flex;
  justify-content: center;
}

.tiled-grid {
  display: grid;
  gap: 1px;
  max-width: 400px;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  overflow: hidden;
}

.tiled-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.tiled-controls {
  text-align: center;
}

.tiled-controls label {
  font-size: 14px;
  color: var(--color-text);
}

.tiled-controls select {
  margin-left: 8px;
  padding: 4px 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
}

.result-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border);
}

.download-btn {
  flex: 1;
  background-color: #8b5cf6;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.download-btn.secondary {
  background-color: #6b7280;
}

.clear-btn {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.processing-content {
  text-align: center;
  max-width: 300px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.processing-steps {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step {
  font-size: 14px;
  color: var(--color-text);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.step.active {
  color: #8b5cf6;
  opacity: 1;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .example-showcase {
    flex-direction: column;
    gap: 16px;
  }
  
  .example-arrow {
    transform: rotate(90deg);
  }
  
  .result-actions {
    flex-direction: column;
  }
  
  .radio-group {
    gap: 12px;
  }
}
</style>
