<template>
  <AppLayout>
    <MainContent>
      <!-- 参数设置面板 -->
      <template #parameters>
        <!-- 原图上传 -->
        <div class="param-group">
            <label class="param-label">
              <span class="required">*</span>
              上传原图
            </label>
            <div class="upload-area" @click="triggerFileInput" @drop="handleDrop" @dragover.prevent>
              <input
                ref="fileInput"
                type="file"
                accept="image/*"
                @change="handleFileSelect"
                style="display: none"
              />
              <div v-if="!selectedFile" class="upload-content">
                <div class="upload-icon">📁</div>
                <p class="upload-text">点击上传原图或拖拽文件到此</p>
                <p class="upload-hint">支持JPG/PNG格式，最大5MB</p>
                <p class="upload-paste-hint">💡 支持拖拽上传和Ctrl+V粘贴</p>
              </div>
              <div v-else class="file-selected">
                <div v-if="filePreview" class="file-preview">
                  <img :src="filePreview" alt="原图预览" class="preview-image" />
                  <button class="remove-file-overlay" @click.stop="removeFile" title="移除图片">✕</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 面料图片上传 -->
          <div class="param-group">
            <label class="param-label">
              <span class="required">*</span>
              上传面料图片
            </label>
            <div class="upload-area" @click="triggerFabricFileInput" @drop="handleFabricDrop" @dragover.prevent>
              <input
                ref="fabricFileInput"
                type="file"
                accept="image/*"
                @change="handleFabricFileSelect"
                style="display: none"
              />
              <div v-if="!selectedFabricFile" class="upload-content">
                <div class="upload-icon">🧵</div>
                <p class="upload-text">点击上传面料图片或拖拽文件到此</p>
                <p class="upload-hint">支持JPG/PNG格式，最大5MB</p>
                <p class="upload-paste-hint">💡 支持拖拽上传</p>
              </div>
              <div v-else class="file-selected">
                <div v-if="fabricFilePreview" class="file-preview">
                  <img :src="fabricFilePreview" alt="面料图片预览" class="preview-image" />
                  <button class="remove-file-overlay" @click.stop="removeFabricFile" title="移除图片">✕</button>
                </div>
              </div>
            </div>
          </div>



          <div class="param-group">
            <label class="param-label">
              <span class="required">*</span>
              输出张数
            </label>
            <div class="number-control-group">
              <button
                type="button"
                class="number-btn decrease"
                @click="decreaseOutputCount"
                :disabled="params.outputCount <= 1"
              >
                -
              </button>
              <span class="number-display">{{ params.outputCount }}</span>
              <button
                type="button"
                class="number-btn increase"
                @click="increaseOutputCount"
                :disabled="params.outputCount >= 10"
              >
                +
              </button>
            </div>
          </div>



          <div class="action-section">
            <button
              class="process-btn"
              :disabled="!canProcess"
              @click="startProcess"
            >
              <span v-if="!processing">开始处理</span>
              <span v-else>
                处理中... ({{ processingProgress.current }}/{{ processingProgress.total }})
              </span>
            </button>
          </div>
      </template>

      <!-- 操作按钮 -->
      <template #actions>
        <button v-if="hasResults" class="clear-btn" @click="clearResults">
          清空结果
        </button>
      </template>

      <!-- 结果展示 -->
      <template #results>
        <div v-if="!hasResults" class="tutorial-section">
          <div class="tutorial-link">
            <a href="#" class="tutorial-btn">
              📖 操作教程(使用前请务必查看)
            </a>
            <p class="tutorial-desc">一键为服装更换面料材质，支持多种面料风格</p>
          </div>

          <div class="example-images">
            <div class="example-item">
              <div class="example-img">
                <div class="example-label">原图</div>
                <div class="example-desc">处理前</div>
              </div>
            </div>
            <div class="example-item">
              <div class="example-img">
                <div class="example-label">换面料</div>
                <div class="example-desc">处理后</div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="results-display">
          <div class="comparison-wrapper">
            <div class="comparison-item">
              <div class="comparison-image-container">
                <img :src="originalImageUrl" alt="原图" class="comparison-img" />
                <div class="image-overlay">
                  <div class="image-info">
                    <span class="image-type">原始图片</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="comparison-divider">
              <div class="divider-line"></div>
              <div class="divider-icon">→</div>
              <div class="divider-line"></div>
            </div>
            
            <div class="comparison-item">
              <div class="comparison-image-container result">
                <img :src="results[selectedResultIndex]?.url" alt="换面料结果" class="comparison-img" />
                <div class="image-overlay">
                  <div class="image-info">
                    <span class="image-type">换面料</span>
                    <span class="version-info">版本 {{ selectedResultIndex + 1 }}</span>
                  </div>
                  <div class="result-actions-overlay">
                    <button class="download-btn-modern" @click="downloadResult(results[selectedResultIndex])">
                      <span>⬇</span>
                      下载
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </template>

      <!-- 底部缩略图选择器 -->
      <template #bottom>
        <div v-if="results.length > 1" class="bottom-thumbnails">
          <div class="thumbnails-container">
            <div
              v-for="(result, index) in results"
              :key="index"
              class="thumbnail-item"
              :class="{ active: selectedResultIndex === index }"
              @click="selectResult(index)"
            >
              <img :src="result.url" :alt="`版本 ${index + 1}`" class="thumbnail-img" />
              <div class="thumbnail-label">{{ index + 1 }}</div>
            </div>
          </div>
        </div>
      </template>
    </MainContent>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import AppLayout from '@/components/layout/AppLayout.vue'
import MainContent from '@/components/layout/MainContent.vue'

// 参数设置 - 服装换面料只需要输出张数
const params = ref({
  outputCount: 1
})



// 原图文件处理
const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(null)
const filePreview = ref<string>('')
const originalImageUrl = ref<string>('')

// 面料图片文件处理
const fabricFileInput = ref<HTMLInputElement>()
const selectedFabricFile = ref<File | null>(null)
const fabricFilePreview = ref('')

// 处理状态
const processing = ref(false)
const processingProgress = ref({ current: 0, total: 0 })
const results = ref<Array<{ url: string; name: string }>>([])

// 选中的结果图片索引
const selectedResultIndex = ref(0)

// 计算属性
const canProcess = computed(() => {
  return selectedFile.value !== null && selectedFabricFile.value !== null && !processing.value
})

const hasResults = computed(() => {
  return results.value.length > 0
})

// 文件选择处理
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    const file = target.files[0]
    selectedFile.value = file
    createFilePreview(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    const file = event.dataTransfer.files[0]
    selectedFile.value = file
    createFilePreview(file)
  }
}

const createFilePreview = (file: File) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    filePreview.value = e.target?.result as string
    originalImageUrl.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

const removeFile = () => {
  selectedFile.value = null
  filePreview.value = ''
  originalImageUrl.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 面料文件处理函数
const triggerFabricFileInput = () => {
  fabricFileInput.value?.click()
}

const handleFabricFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processFabricFile(file)
  }
}

const handleFabricDrop = (event: DragEvent) => {
  event.preventDefault()
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processFabricFile(files[0])
  }
}

const processFabricFile = (file: File) => {
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    alert('请选择图片文件')
    return
  }

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    alert('文件大小不能超过5MB')
    return
  }

  selectedFabricFile.value = file

  // 生成预览
  const reader = new FileReader()
  reader.onload = (e) => {
    fabricFilePreview.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

const removeFabricFile = () => {
  selectedFabricFile.value = null
  fabricFilePreview.value = ''
  if (fabricFileInput.value) {
    fabricFileInput.value.value = ''
  }
}

// 数量控制
const decreaseOutputCount = () => {
  if (params.value.outputCount > 1) {
    params.value.outputCount--
  }
}

const increaseOutputCount = () => {
  if (params.value.outputCount < 10) {
    params.value.outputCount++
  }
}



// 处理图片
const startProcess = async () => {
  if (!selectedFile.value || !selectedFabricFile.value) return

  processing.value = true
  results.value = [] // 清空之前的结果

  try {
    const outputCount = params.value.outputCount
    processingProgress.value = { current: 0, total: outputCount }
    console.log(`开始处理，将生成 ${outputCount} 张图片`)

    // 循环调用API，生成指定数量的图片
    for (let i = 0; i < outputCount; i++) {
      processingProgress.value.current = i + 1
      console.log(`正在生成第 ${i + 1}/${outputCount} 张图片...`)

      // 创建FormData对象
      const formData = new FormData()
      formData.append('file', selectedFile.value)
      formData.append('fabricFile', selectedFabricFile.value)
      formData.append('mode', 'changeTheFabricOfTheClothes')



      // 每次调用生成1张图片
      formData.append('outputCount', '1')

      // 调用后端API
      const response = await fetch('/api/comfyui/generate', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success && result.images && result.images.length > 0) {
        // 处理成功，将结果添加到数组中
        const newImages = result.images.map((img: any, index: number) => ({
          url: img.url,
          name: img.filename || `change_fabric_${results.value.length + index + 1}.png`
        }))

        results.value.push(...newImages)
        console.log(`第 ${i + 1} 张图片生成成功`)
      } else {
        throw new Error(result.error || `第 ${i + 1} 张图片处理失败，未返回有效结果`)
      }

      // 如果不是最后一次调用，添加短暂延迟避免服务器压力
      if (i < outputCount - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    console.log(`所有图片生成完成，共生成 ${results.value.length} 张图片`)
  } catch (error: any) {
    console.error('处理失败:', error)
    // 显示错误提示
    alert(`处理失败: ${error.message}`)
  } finally {
    processing.value = false
  }
}

// 选择结果图片
const selectResult = (index: number) => {
  selectedResultIndex.value = index
}

// 下载结果
const downloadResult = (result: { url: string; name: string }) => {
  const link = document.createElement('a')
  link.href = result.url
  link.download = result.name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 清空结果
const clearResults = () => {
  results.value = []
  selectedResultIndex.value = 0
}

// 粘贴处理
const handlePaste = (event: ClipboardEvent) => {
  const items = event.clipboardData?.items
  if (!items) return

  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    if (item.type.indexOf('image') !== -1) {
      const file = item.getAsFile()
      if (file) {
        selectedFile.value = file
        createFilePreview(file)
        console.log('通过粘贴添加了图片')
      }
      break
    }
  }
}

// 监听结果变化，重置选中索引
watch(results, () => {
  if (results.value.length > 0) {
    selectedResultIndex.value = 0
  }
}, { deep: true })

// 生命周期钩子 - 添加粘贴事件监听
onMounted(() => {
  document.addEventListener('paste', handlePaste)
})

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>

<style scoped>
/* 参数设置样式 */
.param-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 16px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.param-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-heading);
  margin-bottom: 8px;
}

.required {
  color: #ef4444;
  margin-right: 4px;
}

/* 上传区域样式 */
.upload-area {
  border: 2px dashed var(--color-border);
  border-radius: 6px;
  padding: 20px 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--color-background);
}

.upload-area:hover {
  border-color: #4f46e5;
  background-color: #f8f9ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 13px;
  font-weight: 500;
  color: var(--color-heading);
  margin: 0;
}

.upload-hint {
  font-size: 11px;
  color: var(--color-text);
  margin: 0;
}

.upload-paste-hint {
  font-size: 10px;
  color: #6b7280;
  margin: 2px 0 0 0;
  font-style: italic;
}

/* 文件选择后的样式 */
.file-selected {
  display: flex;
  flex-direction: column;
}

.file-preview {
  position: relative;
  display: flex;
  justify-content: center;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.remove-file-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-file-overlay:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

/* 单选按钮样式 */
.radio-group {
  display: flex;
  gap: 16px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: white;
}

.radio-item:hover {
  background-color: #f8f9fa;
}

.radio-item:has(input:checked) {
  background-color: #eef2ff;
  color: #4f46e5;
  font-weight: 500;
}

.select-input {
  padding: 7px 10px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 13px;
  background-color: white;
  transition: border-color 0.2s ease;
  width: 100%;
}

.select-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

/* 滑块样式 */
.slider-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-input {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-input::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-heading);
  min-width: 40px;
  text-align: right;
}

/* 数量控制样式 */
.number-control-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.number-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--color-border);
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.number-btn:hover:not(:disabled) {
  border-color: #4f46e5;
  color: #4f46e5;
}

.number-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.number-display {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-heading);
  min-width: 20px;
  text-align: center;
}



.action-section {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid var(--color-border);
}

.process-btn {
  width: 100%;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.process-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.process-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 结果展示样式 */
.tutorial-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 0;
}

.tutorial-link {
  text-align: center;
}

.tutorial-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #4f46e5;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #4f46e5;
  transition: all 0.2s ease;
}

.tutorial-btn:hover {
  background-color: #4f46e5;
  color: white;
}

.tutorial-desc {
  margin-top: 8px;
  font-size: 14px;
  color: var(--color-text);
}

.example-images {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.example-item {
  text-align: center;
}

.example-img {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  border: 1px solid var(--color-border);
}

.example-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-heading);
  margin-bottom: 4px;
}

.example-desc {
  font-size: 12px;
  color: var(--color-text);
}

/* 对比展示样式 */
.comparison-wrapper {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 24px 0;
  justify-content: center;
}

.comparison-item {
  flex: 1;
  max-width: 400px;
}

.comparison-image-container {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: white;
}

.comparison-img {
  width: 100%;
  height: auto;
  display: block;
  max-height: 400px;
  object-fit: contain;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, transparent 30%, transparent 70%, rgba(0,0,0,0.7) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 16px;
}

.comparison-image-container:hover .image-overlay {
  opacity: 1;
}

.image-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.image-type {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.version-info {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.result-actions-overlay {
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.download-btn-modern {
  background: rgba(79, 70, 229, 0.9);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.download-btn-modern:hover {
  background: rgba(79, 70, 229, 1);
  transform: translateY(-2px);
}

.comparison-divider {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.divider-line {
  width: 20px;
  height: 1px;
  background: var(--color-border);
}

.divider-icon {
  color: var(--color-text);
  font-size: 18px;
  font-weight: 600;
}

/* 底部缩略图选择器样式 */
.bottom-thumbnails {
  padding: 16px 24px;
}

.thumbnails-container {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
}

.thumbnail-item {
  position: relative;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.thumbnail-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.thumbnail-item.active {
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.thumbnail-img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  display: block;
}

.thumbnail-label {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.thumbnail-item.active .thumbnail-label {
  background: #4f46e5;
}

/* 清空按钮样式 */
.clear-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comparison-wrapper {
    flex-direction: column;
    gap: 16px;
  }

  .comparison-divider {
    transform: rotate(90deg);
  }

  .divider-line {
    width: 12px;
  }

  .example-images {
    flex-direction: column;
    align-items: center;
  }

  .thumbnails-container {
    flex-wrap: wrap;
    gap: 8px;
  }

  .thumbnail-img {
    width: 50px;
    height: 50px;
  }
}
</style>
