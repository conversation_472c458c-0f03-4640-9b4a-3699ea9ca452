<template>
  <AppLayout>
    <MainContent>
      <!-- 参数设置面板 -->
      <template #parameters>
        <div class="param-group">
            <label class="param-label">
              <span class="required">*</span>
              上传图片
            </label>
            <div class="upload-area" @click="triggerFileInput" @drop="handleDrop" @dragover.prevent>
              <input
                ref="fileInput"
                type="file"
                accept="image/*"
                @change="handleFileSelect"
                style="display: none"
              />
              <div v-if="!selectedFile" class="upload-content">
                <div class="upload-icon">📁</div>
                <p class="upload-text">点击上传图片或拖拽文件到此</p>
                <p class="upload-hint">支持JPG/PNG格式，最大5MB</p>
                <p class="upload-paste-hint">💡 支持拖拽上传和Ctrl+V粘贴</p>
              </div>
              <div v-else class="file-selected">
                <div v-if="filePreview" class="file-preview">
                  <img :src="filePreview" alt="预览图" class="preview-image" />
                  <button class="remove-file-overlay" @click.stop="removeFile" title="移除图片">✕</button>
                </div>
              </div>
            </div>
          </div>

          <div class="param-group">
            <label class="param-label">
              <span class="required">*</span>
              输出张数
            </label>
            <div class="number-control-group">
              <button
                type="button"
                class="number-btn decrease"
                @click="decreaseOutputCount"
                :disabled="params.outputCount <= 1"
              >
                -
              </button>
              <span class="number-display">{{ params.outputCount }}</span>
              <button
                type="button"
                class="number-btn increase"
                @click="increaseOutputCount"
                :disabled="params.outputCount >= 10"
              >
                +
              </button>
            </div>
          </div>

          <!-- 完全一致参数 -->
          <div class="param-group">
            <label class="param-label">
              完全一致
              <span class="param-value">{{ params.strengthModel.toFixed(2) }}</span>
            </label>
            <div class="slider-container">
              <input
                type="range"
                v-model.number="params.strengthModel"
                min="0"
                max="1"
                step="0.01"
                class="slider"
              />
              <div class="slider-labels">
                <span>0</span>
                <span>1</span>
              </div>
            </div>
          </div>

          <!-- 标签选择 -->
          <div class="param-group">
            <label class="param-label">
              处理标签
              <span class="param-hint">（可多选）</span>
            </label>
            <div v-if="tagsLoading" class="tags-loading">
              <span>加载标签中...</span>
            </div>
            <div v-else-if="tags.length > 0" class="tags-container">
              <div
                v-for="tag in tags"
                :key="tag.id"
                class="tag-item"
                :class="{ active: selectedTagIds.includes(tag.id) }"
                @click="toggleTag(tag.id)"
              >
                {{ tag.name }}
              </div>
            </div>
            <div v-else class="tags-empty">
              <span>暂无可用标签</span>
            </div>
          </div>

          <div class="action-section">
            <button
              class="process-btn"
              :disabled="!canProcess"
              @click="startProcess"
            >
              <span v-if="!processing">开始处理</span>
              <span v-else>
                处理中... ({{ processingProgress.current }}/{{ processingProgress.total }})
              </span>
            </button>
          </div>
      </template>

      <!-- 结果展示面板 -->
      <template #results>
        <div v-if="!hasResults" class="tutorial-section">
            <div class="tutorial-link">
              <a href="#" target="_blank" class="tutorial-btn">
                📖 操作教程(使用前请务必查看)
              </a>
              <p class="tutorial-desc">一键从模特、服装、商品中提取图案或花型</p>
            </div>
            
            <div class="example-images">
              <div class="example-item">
                <div class="example-placeholder before">原图</div>
                <span class="example-label">处理前</span>
              </div>
              <div class="example-item">
                <div class="example-placeholder after">印花</div>
                <span class="example-label">处理后</span>
              </div>
            </div>
          </div>

          <!-- 对比展示区域 -->
          <div v-else class="comparison-wrapper">
            <!-- 原图区域 -->
            <div class="comparison-item">
              <div class="comparison-image-container original">
                <img :src="filePreview" alt="原图" class="comparison-img" />
                <div class="image-overlay">
                  <div class="image-info">
                    <span class="image-type">原始图片</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分隔线 -->
            <div class="comparison-divider">
              <div class="divider-line"></div>
              <div class="divider-icon">→</div>
              <div class="divider-line"></div>
            </div>

            <!-- 结果区域 -->
            <div class="comparison-item">
              <div class="comparison-image-container result">
                <img :src="selectedResult?.url" alt="提取印花结果" class="comparison-img" />
                <div class="image-overlay">
                  <div class="image-info">
                    <span class="image-type">印花提取</span>
                    <span v-if="results.length > 1" class="version-info">版本 {{ selectedResultIndex + 1 }}</span>
                  </div>
                  <div class="result-actions-overlay">
                    <button class="download-btn-modern" @click="downloadImage(selectedResult)">
                      <i class="download-icon">⬇</i>
                      下载
                    </button>
                  </div>
                </div>
              </div>
            </div>

          </div>
      </template>

      <!-- 底部缩略图选择器 -->
      <template #bottom>
        <div v-if="results.length > 1" class="bottom-thumbnails">
          <div class="thumbnails-container">
            <div
              v-for="(result, index) in results"
              :key="index"
              class="thumbnail-item"
              :class="{ active: selectedResultIndex === index }"
              @click="selectResult(index)"
            >
              <img :src="result.url" :alt="`版本 ${index + 1}`" class="thumbnail-img" />
              <div class="thumbnail-label">{{ index + 1 }}</div>
            </div>
          </div>
        </div>
      </template>

      <!-- 操作按钮 -->
      <template #actions>
        <button v-if="hasResults" class="clear-btn" @click="clearResults">
          清空结果
        </button>
      </template>
    </MainContent>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import AppLayout from '@/components/layout/AppLayout.vue'
import MainContent from '@/components/layout/MainContent.vue'

// 参数设置
const params = ref({
  outputCount: 1,
  strengthModel: 1.0
})

// 标签相关
interface Tag {
  id: number
  name: string
}

const tags = ref<Tag[]>([])
const selectedTagIds = ref<number[]>([])
const tagsLoading = ref(false)

// 文件处理
const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(null)
const filePreview = ref<string>('')

// 处理状态
const processing = ref(false)
const processingProgress = ref({ current: 0, total: 0 })
const results = ref<Array<{ url: string; name: string }>>([])

// 选中的结果图片索引
const selectedResultIndex = ref(0)

// 计算属性
const canProcess = computed(() => {
  return selectedFile.value !== null && !processing.value
})

const hasResults = computed(() => {
  return results.value.length > 0
})

// 文件选择处理
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    const file = target.files[0]
    selectedFile.value = file
    createFilePreview(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    const file = event.dataTransfer.files[0]
    selectedFile.value = file
    createFilePreview(file)
  }
}

// 创建文件预览
const createFilePreview = (file: File) => {
  if (file.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      filePreview.value = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  filePreview.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  // 检查是否在输入框中粘贴，如果是则不处理
  const target = event.target as HTMLElement
  if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
    return
  }

  const items = event.clipboardData?.items
  if (!items) return

  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    if (item.type.indexOf('image') !== -1) {
      const file = item.getAsFile()
      if (file) {
        selectedFile.value = file
        createFilePreview(file)

        // 可以添加成功提示
        console.log('图片粘贴成功:', file.name || '剪贴板图片')
        break
      }
    }
  }
}

// 处理逻辑
const startProcess = async () => {
  if (!selectedFile.value) return

  processing.value = true
  results.value = [] // 清空之前的结果

  try {
    const outputCount = params.value.outputCount
    processingProgress.value = { current: 0, total: outputCount }
    console.log(`开始处理，将生成 ${outputCount} 张图片`)

    // 循环调用API，生成指定数量的图片
    for (let i = 0; i < outputCount; i++) {
      processingProgress.value.current = i + 1
      console.log(`正在生成第 ${i + 1}/${outputCount} 张图片...`)

      // 创建FormData对象
      const formData = new FormData()
      formData.append('file', selectedFile.value)
      formData.append('mode', 'patternExtract')

      // 添加其他参数
      formData.append('strengthModel', params.value.strengthModel.toString())

      // 添加选中的标签ID
      if (selectedTagIds.value.length > 0) {
        formData.append('tagIds', JSON.stringify(selectedTagIds.value))
      }

      // 每次调用生成1张图片
      formData.append('outputCount', '1')

      // 调用后端API
      const response = await fetch('/api/comfyui/generate', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success && result.images && result.images.length > 0) {
        // 处理成功，将结果添加到数组中
        const newImages = result.images.map((img: any, index: number) => ({
          url: img.url,
          name: img.filename || `extract_result_${results.value.length + index + 1}.png`
        }))

        results.value.push(...newImages)
        console.log(`第 ${i + 1} 张图片生成成功`)
      } else {
        throw new Error(result.error || `第 ${i + 1} 张图片处理失败，未返回有效结果`)
      }

      // 如果不是最后一次调用，添加短暂延迟避免服务器压力
      if (i < outputCount - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    console.log(`所有图片生成完成，共生成 ${results.value.length} 张图片`)
  } catch (error: any) {
    console.error('处理失败:', error)
    // 显示错误提示
    alert(`处理失败: ${error.message}`)
  } finally {
    processing.value = false
  }
}

// 选择结果图片
const selectResult = (index: number) => {
  selectedResultIndex.value = index
}

// 获取当前选中的结果
const selectedResult = computed(() => {
  return results.value[selectedResultIndex.value] || results.value[0]
})

const downloadImage = async (result: { url: string; name: string }) => {
  try {
    // 获取图片数据
    const response = await fetch(result.url)
    const blob = await response.blob()

    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl

    // 设置文件名，确保有正确的扩展名
    const fileName = result.name || 'extracted-pattern'
    const extension = result.url.includes('.png') ? '.png' : '.jpg'
    link.download = fileName.includes('.') ? fileName : `${fileName}${extension}`

    // 触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)

    console.log('图片下载成功:', link.download)
  } catch (error) {
    console.error('下载失败:', error)
    // 可以添加用户提示
    alert('下载失败，请稍后重试')
  }
}

const clearResults = () => {
  results.value = []
  selectedFile.value = null
  filePreview.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 输出张数控制函数
const decreaseOutputCount = () => {
  if (params.value.outputCount > 1) {
    params.value.outputCount--
  }
}

const increaseOutputCount = () => {
  if (params.value.outputCount < 10) {
    params.value.outputCount++
  }
}

// 标签相关方法
const loadTags = async () => {
  tagsLoading.value = true
  try {
    const response = await fetch('/api/comfyui/tags')
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const result = await response.json()
    if (result.success && result.data && result.data.tags) {
      tags.value = result.data.tags
      // 默认选中所有标签
      selectedTagIds.value = tags.value.map(tag => tag.id)
      console.log('标签加载成功:', tags.value.length, '个标签，已默认全选')
    } else {
      console.warn('标签加载失败:', result.message || '未知错误')
    }
  } catch (error: any) {
    console.error('加载标签失败:', error)
  } finally {
    tagsLoading.value = false
  }
}

const toggleTag = (tagId: number) => {
  const index = selectedTagIds.value.indexOf(tagId)
  if (index > -1) {
    // 如果已选中，则取消选中
    selectedTagIds.value.splice(index, 1)
  } else {
    // 如果未选中，则添加到选中列表
    selectedTagIds.value.push(tagId)
  }
  console.log('当前选中的标签ID:', selectedTagIds.value)
}

// 监听结果变化，重置选中索引
watch(results, (newResults) => {
  if (newResults.length > 0) {
    selectedResultIndex.value = 0
  }
}, { deep: true })

// 生命周期钩子 - 添加粘贴事件监听
onMounted(() => {
  document.addEventListener('paste', handlePaste)
  // 加载标签列表
  loadTags()
})

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>

<style scoped>
/* 参数设置样式 */

.param-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 16px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.param-group:hover {
  background-color: #fafbfc;
}

.param-group:last-child {
  margin-bottom: 0;
}

.param-label {
  font-size: 13px;
  font-weight: 600;
  color: var(--color-heading);
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.required {
  color: #ef4444;
}

.param-value {
  color: #4f46e5;
  font-weight: 700;
  margin-left: auto;
}

/* 滑动条样式 */
.slider-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #6b7280;
  margin-top: 2px;
}

.upload-area {
  border: 2px dashed var(--color-border);
  border-radius: 6px;
  padding: 20px 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--color-background);
}

.upload-area:hover {
  border-color: #4f46e5;
  background-color: #fafbff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.upload-icon {
  font-size: 28px;
}

.upload-text {
  font-size: 13px;
  font-weight: 500;
  color: var(--color-heading);
  margin: 0;
}

.upload-hint {
  font-size: 11px;
  color: var(--color-text);
  margin: 0;
}

.upload-paste-hint {
  font-size: 10px;
  color: #6b7280;
  margin: 2px 0 0 0;
  font-style: italic;
}

/* 文件选择后的样式 */
.file-selected {
  display: flex;
  flex-direction: column;
}

.file-preview {
  position: relative;
  display: flex;
  justify-content: center;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.remove-file-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  color: white;
  font-size: 14px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  opacity: 0.8;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-file-overlay:hover {
  opacity: 1;
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}

.radio-group {
  display: flex;
  gap: 12px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 13px;
  color: var(--color-text);
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

/* 数字控制组件样式 */
.number-control-group {
  display: flex;
  align-items: center;
  gap: 0;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: hidden;
  background: var(--color-background);
  width: fit-content;
}

.number-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.number-btn:hover:not(:disabled) {
  background: var(--color-background-soft);
  color: var(--color-heading);
}

.number-btn:active:not(:disabled) {
  background: var(--color-background-mute);
  transform: scale(0.95);
}

.number-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.number-display {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 32px;
  padding: 0 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-heading);
  background: var(--color-background-soft);
  border-left: 1px solid var(--color-border);
  border-right: 1px solid var(--color-border);
  user-select: none;
}

.radio-item:hover {
  background-color: #f8f9fa;
}

.radio-item:has(input:checked) {
  background-color: #eef2ff;
  color: #4f46e5;
  font-weight: 500;
}

.select-input {
  padding: 7px 10px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 13px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.select-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

/* 标签选择样式 */
.param-hint {
  font-size: 11px;
  color: #6b7280;
  font-weight: normal;
  margin-left: 4px;
}

.tags-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: #6b7280;
  font-size: 13px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  font-size: 13px;
  font-weight: 500;
  color: var(--color-heading);
  white-space: nowrap;
  min-height: 32px;
}

.tag-item:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-item.active {
  border-color: #4f46e5;
  background-color: #4f46e5;
  color: white;
}

.tag-item.active:hover {
  background-color: #3730a3;
  border-color: #3730a3;
}

.tags-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  color: #9ca3af;
  font-size: 13px;
  font-style: italic;
}

.action-section {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid var(--color-border);
}

.process-btn {
  width: 100%;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.process-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.process-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 结果展示样式 */

.tutorial-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 0;
}

.tutorial-link {
  text-align: center;
}

.tutorial-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #4f46e5;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #4f46e5;
  transition: all 0.2s ease;
}

.tutorial-btn:hover {
  background-color: #4f46e5;
  color: white;
}

.tutorial-desc {
  margin-top: 8px;
  font-size: 14px;
  color: var(--color-text);
}

.example-images {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.example-item {
  text-align: center;
}

.example-img {
  width: 150px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid var(--color-border);
}

.example-placeholder {
  width: 150px;
  height: 100px;
  border-radius: 8px;
  border: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.example-placeholder.before {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.example-placeholder.after {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
}

.example-label {
  display: block;
  margin-top: 8px;
  font-size: 14px;
  color: var(--color-text);
}

/* 对比展示样式 */
.comparison-wrapper {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 20px;
  align-items: center;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  gap: 16px;
}



/* 分隔线样式 */
.comparison-divider {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 0 10px;
}

.divider-line {
  width: 2px;
  height: 40px;
  background: linear-gradient(to bottom, transparent, #d1d5db, transparent);
}

.divider-icon {
  font-size: 24px;
  color: #6b7280;
  font-weight: bold;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comparison-image-container {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.comparison-image-container:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.comparison-image-container.original {
  border: 3px solid #e5e7eb;
}

.comparison-image-container.result {
  border: 3px solid #10b981;
}

.comparison-img {
  width: 100%;
  height: auto;
  min-height: 250px;
  max-height: 450px;
  object-fit: contain;
  display: block;
  background: #f9fafb;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, transparent 30%, transparent 70%, rgba(0,0,0,0.7) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 16px;
}

.comparison-image-container:hover .image-overlay {
  opacity: 1;
}

.image-info {
  display: flex;
  justify-content: center;
}

.image-type {
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.result-actions-overlay {
  display: flex;
  justify-content: center;
}

.download-btn-modern {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.download-btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.download-icon {
  font-size: 16px;
}

/* 版本信息样式 */
.version-info {
  margin-left: 8px;
  font-size: 12px;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 底部缩略图选择器样式 */
.bottom-thumbnails {
  padding: 16px 24px;
}

.thumbnails-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  overflow-x: auto;
  padding: 8px 0;
}

.thumbnail-item {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  background: #f9fafb;
}

.thumbnail-item:hover {
  border-color: #d1d5db;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.thumbnail-item.active {
  border-color: #6366f1;
  box-shadow: 0 0 0 1px #6366f1, 0 4px 12px rgba(99, 102, 241, 0.3);
}

.thumbnail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.thumbnail-item:hover .thumbnail-img {
  transform: scale(1.05);
}

.thumbnail-label {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 4px;
  border-radius: 3px;
  min-width: 14px;
  text-align: center;
}

.thumbnail-item.active .thumbnail-label {
  background: #6366f1;
}



.download-btn-small {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 10px;
  border-radius: 50%;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.download-btn-small:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.result-label {
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #4b5563;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 20px;
  border: 1px solid #e5e7eb;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comparison-wrapper {
    padding: 16px;
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .comparison-divider {
    flex-direction: row;
    padding: 10px 0;
  }

  .divider-line {
    width: 60px;
    height: 2px;
    background: linear-gradient(to right, transparent, #d1d5db, transparent);
  }

  .divider-icon {
    transform: rotate(90deg);
  }

  .comparison-img {
    max-height: 300px;
    min-height: 200px;
  }



  .bottom-thumbnails {
    padding: 8px 0;
  }

  .thumbnails-container {
    gap: 8px;
  }

  .thumbnail-item {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .comparison-wrapper {
    padding: 12px;
  }

  .comparison-img {
    min-height: 150px;
    max-height: 250px;
  }

  .bottom-thumbnails {
    padding: 8px 0;
  }

  .thumbnail-item {
    width: 50px;
    height: 50px;
  }

  .thumbnail-label {
    font-size: 9px;
    padding: 1px 3px;
  }
}

.result-item {
  border: 1px solid var(--color-border);
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
}

.result-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.result-actions {
  padding: 12px;
}

.download-btn {
  width: 100%;
  background-color: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.clear-btn {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}


</style>
