<template>
  <AppLayout>
    <MainContent>
      <!-- 参数设置面板 -->
      <template #parameters>
        <div class="enhance-params">
          <div class="param-group">
            <label class="param-label">
              <span class="required">*</span>
              上传图片
            </label>
            <div class="upload-area" @click="triggerFileInput" @drop="handleDrop" @dragover.prevent>
              <input
                ref="fileInput"
                type="file"
                accept="image/*"
                @change="handleFileSelect"
                style="display: none"
              />
              <div v-if="!selectedFile" class="upload-content">
                <div class="upload-icon">🖼️</div>
                <p class="upload-text">点击上传需要转高清的图片</p>
                <p class="upload-hint">支持JPG/PNG格式，最大10MB</p>
                <p class="upload-paste-hint">💡 支持拖拽上传和Ctrl+V粘贴</p>
              </div>
              <div v-else class="file-selected">
                <div v-if="filePreview" class="file-preview">
                  <img :src="filePreview" alt="预览图" class="preview-image" />
                  <button class="remove-file-overlay" @click.stop="removeFile" title="移除图片">✕</button>
                </div>
              </div>
            </div>
          </div>

          <div class="action-section">
            <button
              class="process-btn"
              :disabled="!canProcess"
              @click="startProcess"
            >
              开始转高清
            </button>
          </div>
        </div>
      </template>

      <!-- 结果展示面板 -->
      <template #results>
        <div class="enhance-results">
          <div v-if="!hasResults && !processing" class="tutorial-section">
            <div class="feature-intro">
              <h4>🚀 AI图像超分辨率增强</h4>
              <p class="tutorial-desc">一键将图片转换为高清，智能放大保持清晰度</p>
            </div>

            <div class="example-images">
              <div class="example-item">
                <div class="example-img">
                  <div class="example-label">原图</div>
                  <div class="example-desc">低分辨率</div>
                </div>
              </div>
              <div class="example-item">
                <div class="example-img">
                  <div class="example-label">转高清</div>
                  <div class="example-desc">高分辨率</div>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="results-display">
            <div class="comparison-wrapper">
              <div class="comparison-item">
                <div class="comparison-image-container">
                  <img :src="originalImageUrl" alt="原图" class="comparison-img" />
                  <div class="image-overlay">
                    <div class="image-info">
                      <span class="image-type">原始图片</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="comparison-divider">
                <div class="divider-line"></div>
                <div class="divider-icon">→</div>
                <div class="divider-line"></div>
              </div>

              <div class="comparison-item">
                <div class="comparison-image-container result">
                  <img :src="enhancedImageUrl" alt="转高清结果" class="comparison-img" />
                  <div class="image-overlay">
                    <div class="image-info">
                      <span class="image-type">转高清</span>
                    </div>
                    <div class="result-actions-overlay">
                      <button class="download-btn-modern" @click="downloadResult">
                        <span>⬇</span>
                        下载
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="processing" class="processing-overlay">
            <div class="processing-content">
              <div class="spinner"></div>
              <p>正在进行增强处理...</p>
            </div>
          </div>
        </div>
      </template>

      <!-- 操作按钮 -->
      <template #actions>
        <button v-if="hasResults" class="clear-btn" @click="clearResults">
          重新处理
        </button>
      </template>
    </MainContent>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import AppLayout from '@/components/layout/AppLayout.vue'
import MainContent from '@/components/layout/MainContent.vue'

// 文件处理
const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(null)
const filePreview = ref<string>('')
const originalImageUrl = ref<string>('')

// 处理状态
const processing = ref(false)
const progress = ref(0)
const enhancedImageUrl = ref('')
const resultData = ref<{ url: string; name: string } | null>(null)

// 计算属性
const canProcess = computed(() => {
  return selectedFile.value !== null && !processing.value
})

const hasResults = computed(() => {
  return enhancedImageUrl.value !== ''
})

// 文件选择处理
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    const file = target.files[0]
    selectedFile.value = file
    createFilePreview(file)
    // 创建预览URL
    originalImageUrl.value = URL.createObjectURL(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    const file = event.dataTransfer.files[0]
    selectedFile.value = file
    createFilePreview(file)
    originalImageUrl.value = URL.createObjectURL(file)
  }
}

// 创建文件预览
const createFilePreview = (file: File) => {
  if (file.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      filePreview.value = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}



// 移除文件
const removeFile = () => {
  selectedFile.value = null
  filePreview.value = ''
  originalImageUrl.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 处理粘贴事件
const handlePaste = (event: ClipboardEvent) => {
  // 检查是否在输入框中粘贴，如果是则不处理
  const target = event.target as HTMLElement
  if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
    return
  }

  const items = event.clipboardData?.items
  if (!items) return

  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    if (item.type.indexOf('image') !== -1) {
      const file = item.getAsFile()
      if (file) {
        selectedFile.value = file
        createFilePreview(file)
        // 创建预览URL
        originalImageUrl.value = URL.createObjectURL(file)

        // 可以添加成功提示
        console.log('图片粘贴成功:', file.name || '剪贴板图片')
        break
      }
    }
  }
}

// 处理逻辑
const startProcess = async () => {
  if (!selectedFile.value) return

  processing.value = true
  progress.value = 0

  try {
    console.log('开始转高清处理...')

    // 创建FormData对象
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    formData.append('mode', 'enhanceQuality')
    formData.append('outputCount', '1')

    // 调用后端API
    const response = await fetch('/api/comfyui/generate', {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.success && result.images && result.images.length > 0) {
      // 处理成功
      const image = result.images[0]
      enhancedImageUrl.value = image.url
      resultData.value = {
        url: image.url,
        name: image.filename || 'enhanced_image.png'
      }
      console.log('转高清处理成功')
    } else {
      throw new Error(result.error || '处理失败，未返回有效结果')
    }

  } catch (error: any) {
    console.error('转高清失败:', error)
    alert(`处理失败: ${error.message}`)
  } finally {
    processing.value = false
    progress.value = 100
  }
}

const downloadResult = () => {
  if (resultData.value) {
    const link = document.createElement('a')
    link.href = resultData.value.url
    link.download = resultData.value.name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

const clearResults = () => {
  enhancedImageUrl.value = ''
  originalImageUrl.value = ''
  selectedFile.value = null
  filePreview.value = ''
  resultData.value = null
  progress.value = 0
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 生命周期钩子 - 添加粘贴事件监听
onMounted(() => {
  document.addEventListener('paste', handlePaste)
})

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>

<style scoped>
.enhance-params {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.param-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 16px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.param-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-heading);
  display: flex;
  align-items: center;
  gap: 4px;
}

.required {
  color: #ef4444;
}

.upload-area {
  border: 2px dashed var(--color-border);
  border-radius: 6px;
  padding: 20px 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--color-background);
}

.upload-area:hover {
  border-color: #10b981;
  background-color: #f0fdf4;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 32px;
}

.upload-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-heading);
  margin: 0;
}

.upload-hint {
  font-size: 12px;
  color: var(--color-text);
  margin: 0;
}

.upload-paste-hint {
  font-size: 11px;
  color: #6b7280;
  margin: 4px 0 0 0;
  font-style: italic;
}

/* 文件选择后的样式 */
.file-selected {
  display: flex;
  flex-direction: column;
}

.file-preview {
  position: relative;
  display: flex;
  justify-content: center;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.remove-file-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(239, 68, 68, 0.9);
  border: none;
  color: white;
  font-size: 14px;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  opacity: 0.8;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-file-overlay:hover {
  opacity: 1;
  background: rgba(239, 68, 68, 1);
  transform: scale(1.1);
}



.action-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border);
}

.process-btn {
  width: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.process-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.process-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 结果展示样式 */
.enhance-results {
  position: relative;
  height: 100%;
}

.tutorial-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 0;
}

.feature-intro {
  text-align: center;
}

.feature-intro h4 {
  font-size: 20px;
  margin-bottom: 16px;
  color: var(--color-heading);
}

.tutorial-desc {
  margin-top: 8px;
  font-size: 14px;
  color: var(--color-text);
}

.example-images {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.example-item {
  text-align: center;
}

.example-img {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  border: 1px solid var(--color-border);
}

.example-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-heading);
  margin-bottom: 4px;
}

.example-desc {
  font-size: 12px;
  color: var(--color-text);
}

/* 对比展示样式 */
.comparison-wrapper {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 24px 0;
  justify-content: center;
}

.comparison-item {
  flex: 1;
  max-width: 400px;
}

.comparison-image-container {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: white;
}

.comparison-img {
  width: 100%;
  height: auto;
  display: block;
  max-height: 400px;
  object-fit: contain;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, transparent 30%, transparent 70%, rgba(0,0,0,0.7) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 16px;
}

.comparison-image-container:hover .image-overlay {
  opacity: 1;
}

.image-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.image-type {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.result-actions-overlay {
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.download-btn-modern {
  background: rgba(16, 185, 129, 0.9);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.download-btn-modern:hover {
  background: rgba(16, 185, 129, 1);
  transform: translateY(-2px);
}

.comparison-divider {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.divider-line {
  width: 20px;
  height: 1px;
  background: var(--color-border);
}

.divider-icon {
  color: var(--color-text);
  font-size: 18px;
  font-weight: 600;
}

/* 清空按钮样式 */
.clear-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.processing-content {
  text-align: center;
  max-width: 300px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .comparison-wrapper {
    flex-direction: column;
    gap: 16px;
  }

  .comparison-item {
    max-width: 100%;
  }

  .divider-icon {
    transform: rotate(90deg);
  }

  .example-images {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
