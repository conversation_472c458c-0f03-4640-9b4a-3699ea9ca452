import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/extract-pattern',
    },
    {
      path: '/extract-pattern',
      name: 'extract-pattern',
      component: () => import('../views/ExtractPatternView.vue'),
    },
    {
      path: '/enhance-quality',
      name: 'enhance-quality',
      component: () => import('../views/EnhanceQualityView.vue'),
    },
    {
      path: '/seamless-loop',
      name: 'seamless-loop',
      component: () => import('../views/SeamlessLoopView.vue'),
    },
    {
      path: '/clothing-extraction',
      name: 'clothing-extraction',
      component: () => import('../views/ClothingExtractionView.vue'),
    },
    {
      path: '/change-fabric',
      name: 'change-fabric',
      component: () => import('../views/ChangeFabricView.vue'),
    },

  ],
})

export default router
