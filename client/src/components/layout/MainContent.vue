<template>
  <div class="main-content">
    <!-- 桌面端布局 -->
    <div class="desktop-layout" v-if="!isMobile">
      <!-- 左侧参数设置面板 -->
      <aside class="params-panel">
        <slot name="parameters">
          <!-- 默认参数设置内容 -->
          <div class="empty-params">
            <p>请选择功能模块以显示相应的参数设置</p>
          </div>
        </slot>
      </aside>

      <!-- 右侧结果展示面板 -->
      <main class="results-panel">
        <div class="results-header">
          <h3 class="panel-title">处理结果</h3>
          <div class="panel-actions">
            <slot name="actions">
              <!-- 操作按钮区域 -->
            </slot>
          </div>
        </div>
        <div class="results-content">
          <slot name="results">
            <!-- 默认结果展示内容 -->
            <div class="empty-results">
              <div class="empty-icon">📸</div>
              <h4>等待处理结果</h4>
              <p>上传图片并设置参数后，处理结果将在这里显示</p>
            </div>
          </slot>
        </div>

        <!-- 底部固定区域 -->
        <div class="results-bottom">
          <slot name="bottom">
            <!-- 底部内容，如缩略图选择器 -->
          </slot>
        </div>
      </main>
    </div>

    <!-- 移动端布局 -->
    <div class="mobile-layout" v-if="isMobile">
      <!-- 移动端切换按钮 -->
      <div class="mobile-tabs">
        <button
          class="tab-btn"
          :class="{ active: activeTab === 'params' }"
          @click="activeTab = 'params'"
        >
          参数设置
        </button>
        <button
          class="tab-btn"
          :class="{ active: activeTab === 'results' }"
          @click="activeTab = 'results'"
        >
          处理结果
        </button>
      </div>

      <!-- 移动端面板 -->
      <div class="mobile-panels">
        <div class="mobile-panel" v-show="activeTab === 'params'">
          <slot name="parameters">
            <div class="empty-params">
              <p>请选择功能模块以显示相应的参数设置</p>
            </div>
          </slot>
        </div>

        <div class="mobile-panel" v-show="activeTab === 'results'">
          <div class="mobile-results-content">
            <slot name="results">
              <div class="empty-results">
                <div class="empty-icon">📸</div>
                <h4>等待处理结果</h4>
                <p>上传图片并设置参数后，处理结果将在这里显示</p>
              </div>
            </slot>
          </div>

          <!-- 移动端底部区域 -->
          <div class="mobile-results-bottom">
            <slot name="bottom">
              <!-- 底部内容，如缩略图选择器 -->
            </slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const activeTab = ref<'params' | 'results'>('params')
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 64px);
}

/* 桌面端布局 */
.desktop-layout {
  flex: 1;
  display: flex;
  gap: 12px;
  padding: 12px;
}

/* 参数面板 */
.params-panel {
  width: 270px;
  flex-shrink: 0;
  background: white;
  border-radius: 6px;
  border: 1px solid var(--color-border);
  padding: 12px;
  overflow-y: auto;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 结果面板 */
.results-panel {
  flex: 1;
  background: white;
  border-radius: 6px;
  border: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.results-header {
  padding: 10px 16px;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
}

.results-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.results-bottom {
  flex-shrink: 0;
  border-top: 1px solid var(--color-border);
  background: #f8f9fa;
}

.panel-title {
  font-size: 15px;
  font-weight: 600;
  color: var(--color-heading);
  margin: 0 0 12px 0;
}

.results-header .panel-title {
  margin: 0;
  font-size: 15px;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

/* 空状态样式 */
.empty-params,
.empty-results {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  text-align: center;
}

.empty-params p {
  color: var(--color-text);
  font-size: 14px;
}

.empty-results {
  flex-direction: column;
  color: var(--color-text);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-results h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--color-heading);
}

.empty-results p {
  font-size: 14px;
  color: var(--color-text);
  opacity: 0.8;
}

/* 移动端布局 */
.mobile-layout {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mobile-tabs {
  background: white;
  border-bottom: 1px solid var(--color-border);
  padding: 0 16px;
  display: flex;
}

.tab-btn {
  background: none;
  border: none;
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-btn.active {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
}

.mobile-panels {
  flex: 1;
  overflow: hidden;
}

.mobile-panel {
  height: 100%;
  background: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.mobile-results-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.mobile-results-bottom {
  flex-shrink: 0;
  border-top: 1px solid var(--color-border);
  background: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    height: calc(100vh - 64px);
  }
}

@media (max-width: 1024px) {
  .desktop-layout {
    padding: 16px;
    gap: 16px;
  }

  .params-panel {
    width: 280px;
    padding: 16px;
  }

  .results-content {
    padding: 16px;
  }
}

@media (min-width: 1400px) {
  .desktop-layout {
    padding: 24px;
    gap: 24px;
  }

  .params-panel {
    width: 360px;
    padding: 24px;
  }

  .results-content {
    padding: 24px;
  }
}
</style>
