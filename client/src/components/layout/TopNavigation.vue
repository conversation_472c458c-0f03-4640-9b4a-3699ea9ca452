<template>
  <header class="top-navigation">
    <div class="nav-container">
      <!-- 左侧Logo/标题 -->
      <div class="nav-brand">
        <router-link to="/extract-pattern" class="brand-link">
          <img src="@/assets/logo.svg" alt="Logo" class="brand-logo" />
          <span class="brand-title">图像处理工具</span>
        </router-link>
      </div>

      <!-- 中间导航菜单 -->
      <nav class="nav-menu">
        <router-link 
          to="/extract-pattern" 
          class="nav-item"
          :class="{ active: currentRoute === 'extract-pattern' }"
        >
          提取印花
        </router-link>
        <router-link 
          to="/enhance-quality" 
          class="nav-item"
          :class="{ active: currentRoute === 'enhance-quality' }"
        >
          转高清
        </router-link>
        <router-link
          to="/seamless-loop"
          class="nav-item"
          :class="{ active: currentRoute === 'seamless-loop' }"
        >
          接循环
        </router-link>
        <router-link
          to="/clothing-extraction"
          class="nav-item"
          :class="{ active: currentRoute === 'clothing-extraction' }"
        >
          服装提取
        </router-link>
        <router-link
          to="/change-fabric"
          class="nav-item"
          :class="{ active: currentRoute === 'change-fabric' }"
        >
          服装换面料
        </router-link>
      </nav>

      <!-- 右侧用户区域 -->
      <div class="nav-user">
        <button class="login-btn" @click="handleLogin">
          登录/注册
        </button>
      </div>

      <!-- 移动端菜单按钮 -->
      <button class="mobile-menu-btn" @click="toggleMobileMenu">
        <span class="hamburger"></span>
      </button>
    </div>

    <!-- 移动端菜单 -->
    <div class="mobile-menu" :class="{ open: mobileMenuOpen }">
      <router-link to="/extract-pattern" class="mobile-nav-item" @click="closeMobileMenu">
        提取印花
      </router-link>
      <router-link to="/enhance-quality" class="mobile-nav-item" @click="closeMobileMenu">
        转高清
      </router-link>
      <router-link to="/seamless-loop" class="mobile-nav-item" @click="closeMobileMenu">
        接循环
      </router-link>
      <router-link to="/clothing-extraction" class="mobile-nav-item" @click="closeMobileMenu">
        服装提取
      </router-link>
      <router-link to="/change-fabric" class="mobile-nav-item" @click="closeMobileMenu">
        服装换面料
      </router-link>
      <button class="mobile-login-btn" @click="handleLogin">
        登录/注册
      </button>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const mobileMenuOpen = ref(false)

const currentRoute = computed(() => route.name as string)

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

const handleLogin = () => {
  // TODO: 实现登录逻辑
  console.log('登录/注册')
  closeMobileMenu()
}
</script>

<style scoped>
.top-navigation {
  background-color: #ffffff;
  border-bottom: 1px solid var(--color-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container {
  margin: 0 auto;
  padding: 0 20px;
  height: 64px;
  display: flex;
  align-items: center;
  width: 100%;
}

/* 品牌区域 */
.nav-brand {
  flex-shrink: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--color-heading);
}

.brand-logo {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

.brand-title {
  font-size: 18px;
  font-weight: 600;
}

/* 导航菜单 */
.nav-menu {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-left: 48px;
  flex: 1;
}

.nav-item {
  text-decoration: none;
  color: var(--color-text);
  font-size: 16px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
  position: relative;
}

.nav-item:hover {
  color: var(--color-heading);
  background-color: var(--color-background-soft);
}

.nav-item.active {
  color: #4f46e5;
  background-color: #eef2ff;
}

/* 用户区域 */
.nav-user {
  flex-shrink: 0;
}

.login-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
}

.hamburger {
  width: 20px;
  height: 2px;
  background-color: var(--color-text);
  position: relative;
  transition: all 0.3s ease;
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background-color: var(--color-text);
  transition: all 0.3s ease;
}

.hamburger::before {
  top: -6px;
}

.hamburger::after {
  bottom: -6px;
}

/* 移动端菜单 */
.mobile-menu {
  display: none;
  background-color: white;
  border-top: 1px solid var(--color-border);
  padding: 16px 20px;
  flex-direction: column;
  gap: 12px;
}

.mobile-nav-item {
  text-decoration: none;
  color: var(--color-text);
  font-size: 16px;
  font-weight: 500;
  padding: 12px 0;
  border-bottom: 1px solid var(--color-border);
}

.mobile-login-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 16px;
  }
  
  .nav-menu,
  .nav-user {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .mobile-menu.open {
    display: flex;
  }
  
  .brand-title {
    font-size: 16px;
  }
  
  .brand-logo {
    width: 28px;
    height: 28px;
    margin-right: 8px;
  }
}

@media (max-width: 1024px) {
  .nav-menu {
    gap: 24px;
    margin-left: 32px;
  }
}
</style>
