import axios from 'axios';
import crypto from 'crypto';
import { Request, Response } from 'express';
import dotenv from 'dotenv';

dotenv.config();

/**
 * 生成MD5签名
 */
const generateMD5 = (text: string): string => {
  return crypto.createHash('md5').update(text).digest('hex');
};

/**
 * 翻译文本从中文到英文
 */
export const translateText = async (req: Request, res: Response) => {
  try {
    const { text } = req.body;
    
    // 参数验证
    if (!text) {
      return res.status(400).json({ success: false, message: '缺少翻译文本' });
    }
    
    // 获取配置信息
    const appId = process.env.TRANSLATION_APP_ID;
    const appSecret = process.env.TRANSLATION_APP_SECRET;
    const translateServer = process.env.TRANSLATION_SSERVER || 'https://fanyi-api.baidu.com/api/trans/vip/translate';
    
    if (!appId || !appSecret) {
      return res.status(500).json({ success: false, message: '翻译服务配置缺失' });
    }
    
    // 生成随机数
    const salt = Date.now().toString();
    
    // 生成签名：appid+q+salt+密钥的MD5值
    const sign = generateMD5(appId + text + salt + appSecret);
    
    // 准备请求参数
    const params = new URLSearchParams();
    params.append('q', text);
    params.append('from', 'zh'); // 固定源语言为中文
    params.append('to', 'en');   // 固定目标语言为英文
    params.append('appid', appId);
    params.append('salt', salt);
    params.append('sign', sign);
        
    // 发送请求到百度翻译API
    const response = await axios.post(translateServer, params);
        
    // 检查翻译结果
    if (response.data && response.data.trans_result) {
      return res.status(200).json({
        success: true,
        data: response.data.trans_result
      });
    } else {
      return res.status(400).json({
        success: false,
        message: '翻译失败',
        error: response.data
      });
    }
  } catch (error) {
    console.error('翻译服务错误:', error);
    return res.status(500).json({
      success: false,
      message: '翻译服务错误',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
};
