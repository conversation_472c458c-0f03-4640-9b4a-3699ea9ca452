import axios from 'axios';
import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import FormData from 'form-data';
import { Request, Response } from 'express';

// 类型定义
interface WorkflowNode {
  inputs?: any;
  class_type?: string;
  _meta?: {
    title?: string;
  };
}

interface Workflow {
  [nodeId: string]: WorkflowNode;
}

interface PromptData {
  timestamp: number;
  status: string;
}

// 标签相关类型定义
interface Tag {
  id: number;
  name: string;
  seletedValue: string;
  noSeletedValue: string;
}

interface TagsConfig {
  tags: Tag[];
}



interface GenerateImageResult {
  success: boolean;
  images: Array<{
    url: string;
    filename: string;
    fullPath: string;
  }>;
}

// ComfyUI服务器配置
//const COMFY_SERVER = process.env.COMFY_SERVER || 'http://192.168.11.103:8188'
const COMFY_SERVER: string = process.env.COMFY_SERVER || 'http://122.224.245.130:8188';

const workflows = new Map<string, Workflow>(); // 使用Map存储不同模式的工作流

// 在文件顶部添加
const activePrompts = new Map<string, PromptData>();
// 新增：严格顺序分配prompt_id的队列
const pendingPromptIds: string[] = [];

// 在文件顶部添加
const resultsDir = path.join(__dirname, '..', '..', 'results');
const workflowsDir = path.join(__dirname, '..', 'comfyui');
const configDir = path.join(__dirname, '..', 'config');
const tagsConfigPath = path.join(configDir, 'tags.json');

// 确保results目录存在
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir, { recursive: true });
}

// 确保config目录存在
if (!fs.existsSync(configDir)) {
  fs.mkdirSync(configDir, { recursive: true });
}

// 加载标签配置
function loadTagsConfig(): Tag[] {
  try {
    if (!fs.existsSync(tagsConfigPath)) {
      console.warn(`标签配置文件不存在: ${tagsConfigPath}`);
      return [];
    }
    const configData = fs.readFileSync(tagsConfigPath, 'utf8');
    const config: TagsConfig = JSON.parse(configData);
    console.log(`成功加载 ${config.tags.length} 个标签配置`);
    return config.tags;
  } catch (error) {
    console.error('加载标签配置失败:', error);
    return [];
  }
}

// 加载工作流文件
function loadWorkflow(mode: string): Workflow | null {
  try {
    const workflowPath = path.join(workflowsDir, `${mode}.json`);
    if (!fs.existsSync(workflowPath)) {
      console.error(`工作流文件不存在: ${workflowPath}`);
      return null;
    }
    const workflow: Workflow = JSON.parse(fs.readFileSync(workflowPath, 'utf8'));
    console.log(`成功加载工作流：${mode}.json`);
    return workflow;
  } catch (error) {
    console.error(`加载工作流文件失败 (${mode}.json):`, error);
    return null;
  }
}

// 扫描并加载所有工作流文件
function initializeWorkflows(): { success: boolean; message: string; workflows: string[] } {
  try {
    // 清空现有工作流（支持重新加载）
    workflows.clear();

    // 检查工作流目录是否存在
    if (!fs.existsSync(workflowsDir)) {
      const message = `工作流目录不存在: ${workflowsDir}`;
      console.warn(message);
      return { success: false, message, workflows: [] };
    }

    // 读取目录下所有文件
    const files = fs.readdirSync(workflowsDir);

    // 过滤出JSON文件
    const jsonFiles = files.filter(file =>
      file.endsWith('.json') && fs.statSync(path.join(workflowsDir, file)).isFile()
    );

    console.log(`发现 ${jsonFiles.length} 个工作流文件:`, jsonFiles);

    // 加载每个工作流文件
    let successCount = 0;
    jsonFiles.forEach(file => {
      const mode = path.basename(file, '.json'); // 去掉.json扩展名作为模式名
      const workflow = loadWorkflow(mode);
      if (workflow) {
        workflows.set(mode, workflow);
        console.log(`✓ 成功加载工作流: ${mode}`);
        successCount++;
      } else {
        console.error(`✗ 加载工作流失败: ${mode}`);
      }
    });

    const message = `工作流加载完成，共加载 ${successCount}/${jsonFiles.length} 个工作流`;
    console.log(message);
    console.log('可用的工作流模式:', Array.from(workflows.keys()));

    return {
      success: true,
      message,
      workflows: Array.from(workflows.keys())
    };
  } catch (error: any) {
    const message = `扫描工作流目录时出错: ${error.message}`;
    console.error(message);
    return { success: false, message, workflows: [] };
  }
}

// 初始化时加载所有工作流
initializeWorkflows();

// 导入翻译控制器
import * as translationController from './translationController';

// 添加检测文本是否包含中文的函数
/**
 * 检测文本是否包含中文字符
 * @param text 需要检测的文本
 * @returns 是否包含中文
 */
function containsChinese(text: string): boolean {
  // 中文Unicode字符范围
  const chineseRegex = /[\u4e00-\u9fa5]/;
  return chineseRegex.test(text);
}

/**
 * 翻译中文文本为英文
 * @param text 需要翻译的中文文本
 * @returns 翻译后的英文文本
 */
async function translateToEnglish(text: string): Promise<string> {
  try {
    // 创建一个模拟的请求和响应对象
    const req = { body: { text } };
    let responseData: any = null;

    const res = {
      status: () => res,
      json: (data: any) => {
        responseData = data;
        return data;
      }
    };

    // 调用翻译控制器
    await translationController.translateText(req as any, res as any);

    // 提取翻译结果
    if (responseData && responseData.success && responseData.data) {
      // 合并翻译结果
      const translatedText = responseData.data.map((item: any) => item.dst).join(' ');
      return translatedText;
    }

    return text;  // 如果翻译失败，返回原文
  } catch (error) {
    console.error('翻译过程发生错误:', error);
    // 翻译失败时返回原文
    return text;
  }
}

// 上传图片到ComfyUI
async function uploadImageToComfyUI(imageBuffer: Buffer, filename?: string): Promise<string> {
  try {
    const form = new FormData();
    form.append('image', imageBuffer, {
      filename: filename || 'image.png',
      contentType: 'image/png'
    });
    
    const response = await axios.post(`${COMFY_SERVER}/upload/image`, form, {
      headers: {
        ...form.getHeaders()
      }
    });
    
    return response.data.name;
  } catch (error) {
    console.error('上传图片到ComfyUI失败:', error);
    throw error;
  }
}

// 修改轮询函数，返回Promise
async function pollForImages(promptId: string): Promise<GenerateImageResult> {
  console.log('开始轮询图片生成状态, prompt_id:', promptId);
  
  const maxAttempts = 120; // 增加到60次，给图片更多生成时间
  const interval = 3000; // 每3秒检查一次，增加间隔
  
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      console.log(`轮询尝试 ${attempt+1}/${maxAttempts}...`);
      
      // 获取历史记录
      const historyResponse = await axios.get(`${COMFY_SERVER}/history`);
      const history = historyResponse.data;
      
      // 检查是否有对应的prompt_id
      if (history[promptId]) {
        const promptData = history[promptId];
        console.log(`找到prompt数据，状态:`, promptData.status || '未知');
        
        // 检查执行状态
        if (promptData.status_str === 'error') {
          console.error('ComfyUI工作流执行出错:', promptData.messages);
          throw new Error('ComfyUI工作流执行失败，请检查工作流配置和输入参数');
        }
        
        // 检查输出
        if (promptData.outputs) {
          console.log(`发现输出节点:`, Object.keys(promptData.outputs).join(', '));
          
          for (const nodeId in promptData.outputs) {
            const output = promptData.outputs[nodeId];
            if (output.images && output.images.length > 0) {
              console.log(`发现图片信息:`, output.images.map((img: any) => img.filename).join(', '));

              // 处理每张图片
              for (const img of output.images) {
                try {
                  // 详细记录图片信息
                  console.log('图片信息:', {
                    filename: img.filename,
                    subfolder: img.subfolder || '',
                    type: img.type || '未知'
                  });
                  
                  // 跳过临时图片
                  if (img.type === 'temp') {
                    console.log('跳过临时图片:', img.filename);
                    continue;
                  }
                  
                  // 构建图片URL
                  const imageUrl = `${COMFY_SERVER}/view?filename=${img.filename}&subfolder=${img.subfolder || ''}`;
                  console.log('尝试获取图片:', imageUrl);
                  
                  // 添加重试逻辑获取图片
                  let imageResponse;
                  const maxImageRetries = 3;
                  for (let imageRetry = 0; imageRetry < maxImageRetries; imageRetry++) {
                    try {
                      imageResponse = await axios.get(imageUrl, {
                        responseType: 'arraybuffer',
                        timeout: 10000 // 10秒超时
                      });
                      break; // 成功获取图片，跳出重试循环
                    } catch (imgErr: any) {
                      console.error(`获取图片失败(尝试 ${imageRetry+1}/${maxImageRetries}):`, imgErr.message);
                      if (imageRetry === maxImageRetries - 1) {
                        throw imgErr; // 最后一次重试失败，抛出错误
                      }
                      await new Promise(r => setTimeout(r, 2000)); // 等待2秒再重试
                    }
                  }
                  
                  // 检查响应是否包含有效数据
                  if (!imageResponse || !imageResponse.data || imageResponse.data.length === 0) {
                    console.error('图片响应为空或无效');
                    throw new Error('获取到的图片数据无效');
                  }
                  
                  console.log(`成功获取图片, 大小: ${imageResponse.data.length} 字节`);
                  
                  // 获取文件扩展名
                  const ext = path.extname(img.filename) || '.png'; // 默认为PNG
                  // 使用传入的promptId作为文件名
                  const newFilename = `${promptId}${ext}`;
                  
                  // 保存图片
                  const savePath = path.join(resultsDir, newFilename);
                  console.log('准备保存到:', savePath);
                  
                  try {
                    // 确保目录存在
                    if (!fs.existsSync(resultsDir)) {
                      console.log('创建results目录:', resultsDir);
                      fs.mkdirSync(resultsDir, { recursive: true });
                    }
                    
                    // 保存文件
                    fs.writeFileSync(savePath, imageResponse.data);
                    console.log('文件写入成功');
                    
                    // 设置文件权限
                    fs.chmodSync(savePath, 0o644);
                    console.log('文件权限设置成功');
                    
                    // 验证文件是否真的保存成功
                    if (fs.existsSync(savePath)) {
                      const stats = fs.statSync(savePath);
                      console.log('文件验证成功, 大小:', stats.size);
                    } else {
                      throw new Error('文件保存后未找到');
                    }
                    
                    console.log(`图片已保存: ${newFilename}`);
                  } catch (err: any) {
                    console.error('保存图片失败:', err);
                    console.error('错误详情:', {
                      code: err.code,
                      message: err.message,
                      path: savePath
                    });
                    throw new Error(`保存图片失败: ${err.message}`);
                  }
                  
                  // 更新消息中的图片数据
                  const resultUrl = `/results/${newFilename}`;
                  console.log('生成的图片URL:', resultUrl);
                  img.url = resultUrl;
                  
                  // 返回处理后的图片信息
                  return {
                    success: true,
                    images: [{
                      url: resultUrl,
                      filename: newFilename,
                      fullPath: path.join(resultsDir, newFilename)
                    }]
                  };
                } catch (err: any) {
                  console.error('保存图片时出错:', err.message);
                  if (attempt < maxAttempts - 1) {
                    console.log('将在下一轮轮询中重试');
                    continue; // 继续外部轮询循环
                  }
                  throw err;
                }
              }
            } else {
              console.log('节点没有图片输出或图片数组为空');
            }
          }
        } else {
          console.log('prompt数据中没有输出信息，可能仍在处理中');
        }
      } else {
        console.log(`未找到prompt_id: ${promptId}的记录，可能正在排队或处理中`);
      }
      
      // 等待一段时间后继续下一次轮询
      console.log(`等待${interval/1000}秒后重试...`);
      await new Promise(resolve => setTimeout(resolve, interval));
    } catch (error: any) {
      console.error('轮询出错:', error.message);
      // 在最后一次尝试时才抛出错误
      if (attempt >= maxAttempts - 1) {
        throw error;
      }
      // 否则等待后继续
      console.log(`发生错误，等待${interval/1000}秒后重试...`);
      await new Promise(resolve => setTimeout(resolve, interval));
    }
  }
  
  throw new Error(`轮询超时(${maxAttempts}次尝试)，未找到图片`);
}

// 获取服务器状态
async function getStatus(_req: Request, res: Response): Promise<void> {
  try {
    const response = await axios.get(`${COMFY_SERVER}/system_stats`);
    res.json({ status: 'online', data: response.data });
  } catch (error: any) {
    res.status(500).json({ status: 'offline', error: error.message });
  }
}

// 获取标签列表
function getTags(_req: Request, res: Response): void {
  try {
    const tags = loadTagsConfig();
    res.json({
      success: true,
      data: {
        tags: tags.map(tag => ({ id: tag.id, name: tag.name })),
        count: tags.length
      },
      message: `成功获取 ${tags.length} 个标签`
    });
  } catch (error: any) {
    console.error('获取标签列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取标签列表失败',
      message: error.message
    });
  }
}

// 获取单个工作流配置
function getWorkflow(req: Request, res: Response): void {
  const mode = req.params.mode;

  // 参数验证
  if (!mode) {
    res.status(400).json({
      success: false,
      error: '缺少工作流模式参数',
      message: '请提供有效的工作流模式名称'
    });
    return;
  }

  const workflow = workflows.get(mode);
  if (workflow) {
    res.json({
      success: true,
      data: {
        mode: mode,
        workflow: workflow
      },
      message: `成功获取工作流: ${mode}`
    });
  } else {
    // 提供可用的工作流列表作为提示
    const availableWorkflows = Array.from(workflows.keys());
    res.status(404).json({
      success: false,
      error: `工作流未找到: ${mode}`,
      message: `可用的工作流: ${availableWorkflows.join(', ')}`,
      availableWorkflows: availableWorkflows
    });
  }
}

// 获取所有可用工作流列表的控制器
function getWorkflowList(_req: Request, res: Response): void {
  const workflowList = Array.from(workflows.keys());
  res.json({
    success: true,
    data: {
      workflows: workflowList,
      count: workflowList.length
    },
    message: `共有 ${workflowList.length} 个可用工作流`
  });
}

// 重新加载工作流的控制器
function reloadWorkflows(_req: Request, res: Response): void {
  const result = initializeWorkflows();
  if (result.success) {
    res.json({
      success: true,
      data: {
        workflows: result.workflows,
        count: result.workflows.length
      },
      message: result.message
    });
  } else {
    res.status(500).json({
      success: false,
      error: result.message
    });
  }
}

// 扩展Request接口以包含file属性
interface RequestWithFile extends Request {
  file?: any;
  files?: any;
  body: {
    mode?: string;
    image?: string;
    strengthModel?: number;
    tagIds?: number[]; // 新增：选中的标签ID数组
  };
}

// 修改生成图片函数
async function generateImage(req: RequestWithFile, res: Response): Promise<void> {
  // 获取生成模式
  const mode = req.body.mode || 'text2img';
  const strength_model = req.body.strengthModel || 1;
  console.log('当前生成模式:', mode);

  // 获取对应模式的工作流
  let workflow = workflows.get(mode);
  if (!workflow) {
    // 如果工作流未加载，尝试重新加载
    const loadedWorkflow = loadWorkflow(mode);
    if (!loadedWorkflow) {
      console.error(`工作流未加载: ${mode}`);
      res.status(400).json({ error: `工作流未加载: ${mode}` });
      return;
    }
    workflow = loadedWorkflow;
    workflows.set(mode, workflow);
  }

  try {
    // 创建工作流的副本，避免修改原始工作流
    const workflowCopy = JSON.parse(JSON.stringify(workflow));

    // 处理图片数据
    let uploadedImageName = '';
    let uploadedFabricName = '';
    let prompt: string | null = 'Extract the main pattern and present it as a seamless. maintaining the pattern consistency. All edges are complete, and the image is visually independent. ';

    // 处理标签ID
    let selectedTags: Tag[] = [];
    if (req.body.tagIds) {
      try {
        // 如果tagIds是字符串，尝试解析为JSON
        const tagIds = typeof req.body.tagIds === 'string'
          ? JSON.parse(req.body.tagIds)
          : req.body.tagIds;

        if (Array.isArray(tagIds) && tagIds.length > 0) {
          const allTags = loadTagsConfig();
          selectedTags = allTags.filter(tag => tagIds.includes(tag.id));
          // 根据id排序
          selectedTags.sort((a, b) => a.id - b.id);
          // 循环
          selectedTags.forEach(tag => {
            prompt += tag.seletedValue;
          });
          // 如果没有id=3的被选中
          if (!selectedTags.find(tag => tag.id === 3)) {
            prompt += allTags.find(tag => tag.id === 3)?.noSeletedValue || '';
          }

          prompt += ' maintaining a consistent look, without any indication of creases.'
        }
      } catch (error) {
        console.error('解析标签ID失败:', error);
      }
    }

    // 如果上传了图片或提供了base64图像，处理图片数据
    const hasFile = req.file || (req.files && (req.files as any).file && (req.files as any).file[0]) || req.body.image;
    if (hasFile) {
      try {
        if (mode === 'patternExtract' || mode === 'changeTheFabricOfTheClothes'  || mode === 'clothingExtraction' || mode === 'squareCycleDiagram' || mode === 'enhanceQuality') {
          // 处理常规模式的图像
          let imageBuffer: Buffer;
          if (req.file) {
            imageBuffer = fs.readFileSync(req.file.path);
          } else if (req.files && (req.files as any).file && (req.files as any).file[0]) {
            imageBuffer = fs.readFileSync((req.files as any).file[0].path);
          } else {
            imageBuffer = Buffer.from(req.body.image || '', 'base64');
          }

          // 生成随机文件名
          const timestamp = Date.now();
          const randomStr = Math.random().toString(36).substring(2, 8);
          const imageName = `input_${timestamp}_${randomStr}.png`;

          // 上传原图到ComfyUI
          uploadedImageName = await uploadImageToComfyUI(imageBuffer, imageName);
          console.log('上传原图到ComfyUI成功:', uploadedImageName);

          if (mode === 'changeTheFabricOfTheClothes') {
            // 处理换面料的图像
            let imageFabricBuffer: Buffer;
            if (req.files && (req.files as any).fabricFile && (req.files as any).fabricFile[0]) {
              imageFabricBuffer = fs.readFileSync((req.files as any).fabricFile[0].path);
            } else {
              throw new Error('服装换面料模式需要上传面料图片');
            }

            // 生成随机文件名
            const fabricTimestamp = Date.now();
            const fabricRandomStr = Math.random().toString(36).substring(2, 8);
            const fabricImageName = `fabric_${fabricTimestamp}_${fabricRandomStr}.png`;

            // 上传面料图片到ComfyUI
            uploadedFabricName = await uploadImageToComfyUI(imageFabricBuffer, fabricImageName);
          }
        }
      } catch (error: any) {
        console.error('处理图片失败:', error);
        res.status(500).json({ error: '处理图片失败: ' + error.message });
        return;
      }
    }
    
    // 处理所有节点
    for (const nodeId in workflowCopy) {
      const node = workflowCopy[nodeId];

      // 处理seed节点, 随机生成seed
      if (node.inputs && node.inputs.seed) {
        node.inputs.seed = Math.floor(Math.random() * 2147483647);
      }
      
      if (node.class_type === 'LoraLoaderModelOnly' && mode === 'patternExtract') {
        node.inputs.strength_model = strength_model;
      }

      if (node.class_type === 'Primitive string multiline [Crystools]' && mode === 'patternExtract') {
        node.inputs.string = prompt;
      }
      // 处理LoadImage节点
      if (node.class_type === 'LoadImage') {
        if (uploadedImageName && node._meta.title === '加载图像') {
          workflowCopy[nodeId] = {
            inputs: {
              image: uploadedImageName,
              upload: "image"
            },
            class_type: "LoadImage",
            _meta: {
              title: "加载图像"
            }
          };
        } 
        if (uploadedFabricName && node._meta.title === '加载面料图像') {
          workflowCopy[nodeId] = {
            inputs: {
              image: uploadedFabricName,
              upload: "image"
            },
            class_type: "LoadImage",
            _meta: {
              title: "加载面料图像"
            }
          };
        }
      }
    }
    // 发送请求到ComfyUI
    let promptResponse;
    try {
      promptResponse = await axios.post(`${COMFY_SERVER}/prompt`, {
        prompt: workflowCopy
      });
      console.log('ComfyUI响应:', promptResponse.data);
    } catch (error: any) {
      console.error('ComfyUI请求失败:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        error: error.message
      });
      throw error;
    }

    if (!promptResponse || !promptResponse.data) {
      throw new Error('ComfyUI响应无效');
    }

    const promptId = promptResponse.data.prompt_id;
    
    // 存储prompt_id
    activePrompts.set(promptId, {
      timestamp: Date.now(),
      status: 'pending'
    });
    pendingPromptIds.push(promptId);
    
    // 等待图片生成和下载完成
    const result = await pollForImages(promptId);    
    
    // 返回结果
    res.json(result);
  } catch (error: any) {
    console.error('生成图片时出错:', error);
    if (error.response) {
      console.error('ComfyUI响应状态:', error.response.status);
      console.error('ComfyUI响应数据:', error.response.data);
    }
    res.status(500).json({ error: error.message });
  }
}

export {
  getStatus,
  getWorkflow,
  getWorkflowList,
  reloadWorkflows,
  generateImage,
  getTags
};