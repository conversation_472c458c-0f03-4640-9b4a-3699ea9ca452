{"1": {"inputs": {"trans_switch": false, "trans_text": "", "translator": "Baidu", "source_language": "auto", "target_language": "English(en)", "Show / Hide button": "button_show", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text_Translation_V2_Full", "_meta": {"title": "Text Translation V2 (Full)"}}, "2": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "VAE加载器"}}, "4": {"inputs": {"conditioning": ["20", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "6": {"inputs": {"samples": ["12", 0], "vae": ["2", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "7": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "8": {"inputs": {"separator": "", "text1": ["22", 0], "text2": ["1", 0]}, "class_type": "CR Text Concatenate", "_meta": {"title": "文本联结"}}, "9": {"inputs": {"preview": "", "source": ["8", 0]}, "class_type": "PreviewAny", "_meta": {"title": "预览任意"}}, "12": {"inputs": {"seed": 540697104476683, "steps": 25, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["32", 0], "positive": ["18", 0], "negative": ["4", 0], "latent_image": ["17", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "16": {"inputs": {"upscale_method": "lanc<PERSON>s", "megapixels": 1.5000000000000002, "image": ["23", 0]}, "class_type": "ImageScaleToTotalPixels", "_meta": {"title": "图像按像素缩放"}}, "17": {"inputs": {"pixels": ["16", 0], "vae": ["2", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "18": {"inputs": {"guidance": 2.5, "conditioning": ["19", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "19": {"inputs": {"conditioning": ["20", 0], "latent": ["17", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "20": {"inputs": {"text": ["8", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["32", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码器"}}, "22": {"inputs": {"text": "Simply extract the patterns and text on the fabric, turn the patterns into vectors, remove folds, buttons, seams and shadows", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text", "_meta": {"title": "文本"}}, "23": {"inputs": {"image": "80494e416b6a5943de6eb89fe2e03854.jpg"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "24": {"inputs": {"unet_name": "F.1 Kontext dev_fp8.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "UNET加载器"}}, "30": {"inputs": {"filename_prefix": "花纹提取素材/1", "images": ["36", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "31": {"inputs": {"images": ["6", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "32": {"inputs": {"lora_name": "布料花纹提取.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["24", 0], "clip": ["7", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "LoRA加载器"}}, "36": {"inputs": {"supir_model": "SUPIR-v0F_fp16.safetensors", "sdxl_model": "DreamShaper XL v2 Turbo DPMpp .safetensors", "seed": 268610349352746, "resize_method": "lanc<PERSON>s", "scale_by": 1, "steps": 45, "restoration_scale": -1, "cfg_scale": 4, "a_prompt": "high quality, detailed", "n_prompt": "bad quality, blurry, messy", "s_churn": 5, "s_noise": 1.003, "control_scale": 1, "cfg_scale_start": 4, "control_scale_start": 0, "color_fix_type": "Wavelet", "keep_model_loaded": false, "use_tiled_vae": false, "encoder_tile_size_pixels": 512, "decoder_tile_size_latent": 64, "diffusion_dtype": "auto", "encoder_dtype": "auto", "batch_size": 1, "use_tiled_sampling": false, "sampler_tile_size": 1024, "sampler_tile_stride": 512, "fp8_unet": false, "fp8_vae": false, "sampler": "RestoreEDMSampler", "speak_and_recognation": {"__value__": [false, true]}, "image": ["6", 0]}, "class_type": "SUPIR_Upscale", "_meta": {"title": "SUPIR放大"}}}