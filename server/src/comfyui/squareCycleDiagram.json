{"3": {"inputs": {"channel": "red", "image": ["43", 0]}, "class_type": "ImageToMask", "_meta": {"title": "图像到遮罩"}}, "4": {"inputs": {"images": ["7", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "5": {"inputs": {"images": ["9", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "6": {"inputs": {"mask": ["66", 0]}, "class_type": "MaskToImage", "_meta": {"title": "遮罩到图像"}}, "7": {"inputs": {"x": 1020, "y": 0, "resize_source": false, "destination": ["9", 0], "source": ["52", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "图像遮罩复合"}}, "8": {"inputs": {"x": 1020, "y": 1020, "resize_source": false, "destination": ["7", 0], "source": ["52", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "图像遮罩复合"}}, "9": {"inputs": {"x": 0, "y": 1020, "resize_source": false, "destination": ["48", 0], "source": ["52", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "图像遮罩复合"}}, "10": {"inputs": {"images": ["8", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "18": {"inputs": {"model": ["40", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "差异扩散"}}, "19": {"inputs": {"text": "divisions,broken pattern,ugly, deformed, noisy, blurry", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["69", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码器"}}, "20": {"inputs": {"pixels": ["22", 0], "vae": ["23", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "21": {"inputs": {"kernel_size": 10, "sigma": 7, "mask": ["66", 0]}, "class_type": "ImpactGaussianBlurMask", "_meta": {"title": "遮罩高斯模糊"}}, "22": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["8", 0], "source": ["52", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "图像遮罩复合"}}, "23": {"inputs": {"vae_name": "flux-vae-bf16.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "VAE加载器"}}, "24": {"inputs": {"noise_mask": true, "positive": ["49", 0], "negative": ["19", 0], "vae": ["23", 0], "pixels": ["22", 0], "mask": ["21", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "内补模型条件"}}, "31": {"inputs": {"filename_prefix": "ComfyUI", "images": ["38", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "38": {"inputs": {"samples": ["65", 0], "vae": ["23", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "40": {"inputs": {"unet_name": "flux1-schnell-fp8-e4m3fn.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "UNET加载器"}}, "43": {"inputs": {"width": 1276, "height": 1276, "x": 0, "y": 0, "image": ["51", 0]}, "class_type": "ImageCrop", "_meta": {"title": "图像裁剪"}}, "44": {"inputs": {"overlay_resize": "None", "resize_method": "nearest-exact", "rescale_factor": 1, "width": 256, "height": 256, "x_offset": -1020, "y_offset": -1020, "rotation": 0, "opacity": 0, "base_image": ["45", 0], "overlay_image": ["38", 0], "optional_mask": ["3", 0]}, "class_type": "Image Overlay", "_meta": {"title": "图像覆盖"}}, "45": {"inputs": {"overlay_resize": "None", "resize_method": "nearest-exact", "rescale_factor": 1, "width": 1280, "height": 256, "x_offset": 0, "y_offset": -1020, "rotation": 0, "opacity": 0, "base_image": ["46", 0], "overlay_image": ["38", 0], "optional_mask": ["3", 0]}, "class_type": "Image Overlay", "_meta": {"title": "图像覆盖"}}, "46": {"inputs": {"overlay_resize": "None", "resize_method": "nearest-exact", "rescale_factor": 1, "width": 256, "height": 1280, "x_offset": -1020, "y_offset": 0, "rotation": 0, "opacity": 0, "base_image": ["38", 0], "overlay_image": ["38", 0], "optional_mask": ["3", 0]}, "class_type": "Image Overlay", "_meta": {"title": "图像覆盖"}}, "48": {"inputs": {"width": 1276, "height": 1276, "batch_size": 1, "color": 999999}, "class_type": "EmptyImage", "_meta": {"title": "空图像"}}, "49": {"inputs": {"text": "Continuous,Seamless pattern", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["69", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码器"}}, "50": {"inputs": {"images": ["22", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "51": {"inputs": {"upscale_method": "bilinear", "width": 1590, "height": 1590, "crop": "disabled", "image": ["6", 0]}, "class_type": "ImageScale", "_meta": {"title": "图像缩放"}}, "52": {"inputs": {"width": 1024, "height": 1024, "interpolation": "nearest", "method": "stretch", "condition": "always", "multiple_of": 0, "image": ["67", 0]}, "class_type": "ImageResize+", "_meta": {"title": "图像缩放"}}, "53": {"inputs": {"rgthree_comparer": {"images": [{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_hbkpx_00001_.png&type=temp&subfolder=&rand=0.8633996986188062"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_hbkpx_00002_.png&type=temp&subfolder=&rand=0.29787497106672267"}]}, "image_a": ["38", 0], "image_b": ["22", 0]}, "class_type": "Image Comparer (rgthree)", "_meta": {"title": "图像对比"}}, "55": {"inputs": {"images": ["52", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "60": {"inputs": {"width": 1020, "height": 1020, "x": 0, "y": 0, "image": ["44", 0]}, "class_type": "ImageCrop", "_meta": {"title": "图像裁剪"}}, "61": {"inputs": {"upscale_method": "bilinear", "width": 1024, "height": 1024, "crop": "disabled", "image": ["60", 0]}, "class_type": "ImageScale", "_meta": {"title": "图像缩放"}}, "64": {"inputs": {"filename_prefix": "ComfyUI", "images": ["61", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "65": {"inputs": {"seed": "-1", "sampler": "euler", "scheduler": "simple", "steps": "20", "guidance": "20", "max_shift": "", "base_shift": "", "denoise": "1", "model": ["18", 0], "conditioning": ["24", 0], "latent_image": ["24", 2]}, "class_type": "FluxSamplerParams+", "_meta": {"title": "🔧 Flux Sampler Parameters"}}, "66": {"inputs": {"image": "遮罩.png", "channel": "alpha"}, "class_type": "LoadImageMask", "_meta": {"title": "加载图像遮罩"}}, "67": {"inputs": {"image": "屏幕截图_21-7-2025_105059_detail.tmall.com.jpeg"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "69": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}}