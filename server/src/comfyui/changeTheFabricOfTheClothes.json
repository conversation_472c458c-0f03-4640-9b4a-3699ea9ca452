{"11": {"inputs": {"unet_name": "Flux Fill dev - fp8_黑森林官方重绘模型.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "UNET加载器"}}, "14": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "VAE加载器"}}, "15": {"inputs": {"seed": 698623337995427, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["28", 0], "positive": ["22", 0], "negative": ["21", 1], "latent_image": ["21", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "16": {"inputs": {"text": "", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["56", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码器"}}, "17": {"inputs": {"text": "", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["56", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码器"}}, "19": {"inputs": {"samples": ["15", 0], "vae": ["14", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "20": {"inputs": {"images": ["19", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "21": {"inputs": {"noise_mask": true, "positive": ["23", 0], "negative": ["17", 0], "vae": ["14", 0], "pixels": ["34", 0], "mask": ["34", 1]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "内补模型条件"}}, "22": {"inputs": {"guidance": 30, "conditioning": ["21", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "23": {"inputs": {"downsampling_factor": 1, "downsampling_function": "area", "mode": "center crop (square)", "weight": 1, "autocrop_margin": 0.1, "conditioning": ["16", 0], "style_model": ["24", 0], "clip_vision": ["25", 0], "image": ["43", 0]}, "class_type": "ReduxAdvanced", "_meta": {"title": "ReduxAdvanced"}}, "24": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "风格模型加载器"}}, "25": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "CLIP视觉加载器"}}, "27": {"inputs": {"lora_name": "ACE++ comfyui_subject_lora16.safetensors", "strength_model": 1, "model": ["11", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoRA加载器(仅模型)"}}, "28": {"inputs": {"model": ["27", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "差异扩散"}}, "29": {"inputs": {"image": "pasted/image (24).png"}, "class_type": "LoadImage", "_meta": {"title": "加载面料图像"}}, "30": {"inputs": {"image": "模特.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "34": {"inputs": {"direction": "left-right", "pixels": 0, "method": "auto", "image_1": ["43", 0], "image_2": ["44", 0], "mask_2": ["53", 1]}, "class_type": "easy makeImageForICLora", "_meta": {"title": "制作ICLora图像"}}, "35": {"inputs": {"images": ["34", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "42": {"inputs": {"mask": ["34", 1]}, "class_type": "MaskPreview+", "_meta": {"title": "遮罩预览"}}, "43": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 1024, "background_color": "#000000", "image": ["29", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "按宽高比缩放_V2"}}, "44": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 1024, "background_color": "#000000", "image": ["30", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "按宽高比缩放_V2"}}, "50": {"inputs": {"width": ["34", 3], "height": ["34", 4], "position": "top-left", "x_offset": ["34", 5], "y_offset": ["34", 6], "image": ["19", 0]}, "class_type": "ImageCrop+", "_meta": {"title": "图像裁剪"}}, "53": {"inputs": {"sam_model": "sam_vit_h (2.56GB)", "grounding_dino_model": "GroundingDINO_SwinT_OGC (694MB)", "threshold": 0.3, "detail_method": "VITMatte", "detail_erode": 10, "detail_dilate": 10, "black_point": 0.15, "white_point": 0.99, "process_detail": true, "prompt": "coat", "device": "cuda", "max_megapixels": 2, "cache_model": false, "image": ["44", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V2", "_meta": {"title": "SegmentAnything Ultra V2"}}, "54": {"inputs": {"mask": ["53", 1]}, "class_type": "MaskPreview+", "_meta": {"title": "遮罩预览"}}, "56": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "58": {"inputs": {"filename_prefix": "ComfyUI", "images": ["50", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}}