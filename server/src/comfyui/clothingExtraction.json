{"193": {"inputs": {"unet_name": "F.1 Kontext dev_fp8.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "UNET加载器"}}, "195": {"inputs": {"text": ["216", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["238", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码器"}}, "199": {"inputs": {"direction": "right", "match_image_size": true, "spacing_width": 0, "spacing_color": "white", "image1": ["200", 0]}, "class_type": "ImageStitch", "_meta": {"title": "Image Stitch"}}, "200": {"inputs": {"image": "模特.png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "204": {"inputs": {"seed": 272960777575189, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["214", 0], "positive": ["208", 0], "negative": ["210", 0], "latent_image": ["219", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "205": {"inputs": {"samples": ["204", 0], "vae": ["206", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "206": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "VAE加载器"}}, "207": {"inputs": {"pixels": ["236", 0], "vae": ["206", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "208": {"inputs": {"guidance": 2.5, "conditioning": ["209", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "209": {"inputs": {"conditioning": ["195", 0], "latent": ["207", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "210": {"inputs": {"conditioning": ["195", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "211": {"inputs": {"filename_prefix": "ComfyUI", "images": ["205", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "214": {"inputs": {"lora_name": "绘梦Kontext服装产品提取器_衣服服饰3D立体电商白底图_V1.safetensors", "strength_model": 1, "model": ["193", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoRA加载器(仅模型)"}}, "216": {"inputs": {"string": "Extract only the [upper garment] from the input image and generate a front-facing, complete, and isolated 3D rendering of just the upper garment on a pure white background. Do not include any pants, skirts, shorts, or other lower garments. No people, models, mannequins, props, or accessories. The rendering should clearly show the structure, fabric texture, and realistic lighting. The final result must be clean, professional, and suitable for official e-commerce display. -- 3D product extraction style\n", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Primitive string multiline [Crystools]", "_meta": {"title": "字符串元节点(多行)"}}, "217": {"inputs": {"text_0": "Extract only the [upper garment] from the input image and generate a front-facing, complete, and isolated 3D rendering of just the upper garment on a pure white background. Do not include any pants, skirts, shorts, or other lower garments. No people, models, mannequins, props, or accessories. The rendering should clearly show the structure, fabric texture, and realistic lighting. The final result must be clean, professional, and suitable for official e-commerce display. -- 3D product extraction style\n", "text": ["216", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "展示文本"}}, "219": {"inputs": {"width": 960, "height": 1408, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent"}}, "236": {"inputs": {"image": ["199", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "238": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}}