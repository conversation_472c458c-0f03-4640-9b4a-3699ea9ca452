{"193": {"inputs": {"unet_name": "F.1 Kontext dev_fp8.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "UNET加载器"}}, "194": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "195": {"inputs": {"text": ["216", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["194", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码器"}}, "199": {"inputs": {"direction": "right", "match_image_size": true, "spacing_width": 0, "spacing_color": "white", "image1": ["200", 0]}, "class_type": "ImageStitch", "_meta": {"title": "Image Stitch"}}, "200": {"inputs": {"image": "pasted/image (22).png"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "204": {"inputs": {"seed": 490917967838194, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["214", 0], "positive": ["208", 0], "negative": ["210", 0], "latent_image": ["219", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "205": {"inputs": {"samples": ["204", 0], "vae": ["206", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "206": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "VAE加载器"}}, "207": {"inputs": {"pixels": ["212", 0], "vae": ["206", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "208": {"inputs": {"guidance": 2.5, "conditioning": ["209", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "209": {"inputs": {"conditioning": ["195", 0], "latent": ["207", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "210": {"inputs": {"conditioning": ["195", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "211": {"inputs": {"filename_prefix": "ComfyUI", "images": ["205", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}, "212": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 800, "background_color": "#000000", "image": ["199", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "按宽高比缩放_V2"}}, "214": {"inputs": {"lora_name": "绘梦Kontext矢量魔方 _ 循环图案提取器 _ 印花提取_V1.safetensors", "strength_model": 1.0000000000000002, "model": ["193", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoRA加载器(仅模型)"}}, "216": {"inputs": {"string": "Extract the main pattern and present it as a seamless. maintaining the pattern consistency。 All edges are complete, and the image is visually independent.  Eliminate folds completely on the new background, which should use the original background color, ensuring no trace of folds is visible. The new background should match the style of the original background, maintaining a consistent look without any indication of creases seamless seamless seamless, repeatable vector pattern, tileable background, no borders", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Primitive string multiline [Crystools]", "_meta": {"title": "字符串元节点(多行)"}}, "219": {"inputs": {"width": 960, "height": 1408, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "空Latent"}}}