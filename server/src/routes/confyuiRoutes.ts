import express from 'express';
import path from 'path';
import fs from 'fs';
import * as comfyuiController from '../controllers/confyuiController';

// 使用require导入multer以避免类型问题
const multer = require('multer');

const router = express.Router();

// 创建上传文件的存储配置
const storage = multer.diskStorage({
  destination: (req: any, file: any, cb: any) => {
    const uploadsDir = path.join(__dirname, '..', '..', 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    cb(null, uploadsDir);
  },
  filename: (req: any, file: any, cb: any) => {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});

const upload = multer({ storage });

// 路由定义
router.get('/status', comfyuiController.getStatus);
router.get('/workflow/:mode', comfyuiController.getWorkflow);
router.get('/workflows', comfyuiController.getWorkflowList);
router.post('/workflows/reload', comfyuiController.reloadWorkflows);
router.get('/tags', comfyuiController.getTags); // 新增：获取标签列表
router.post('/generate', upload.fields([
  { name: 'file', maxCount: 1 },
  { name: 'fabricFile', maxCount: 1 }
]), comfyuiController.generateImage);

export default router;