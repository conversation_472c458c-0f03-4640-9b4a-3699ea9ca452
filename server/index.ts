import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';
import confyuiRoutes from './src/routes/confyuiRoutes';

// 加载环境变量
dotenv.config();

const app = express();
const PORT: number = parseInt(process.env.PORT || '3011', 10);

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务 - 用于提供生成的图片
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/results', express.static(path.join(__dirname, 'results')));

// 基本路由
app.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'Image Processing Tools API Server',
    version: '1.0.0',
    status: 'running'
  });
});

// API路由
app.use('/api/comfyui', confyuiRoutes);

app.get('/api/health', (req: Request, res: Response) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 示例API端点
app.get('/api/images', (req: Request, res: Response) => {
  res.json({
    message: 'Images endpoint',
    data: []
  });
});

app.post('/api/images/upload', (req: Request, res: Response) => {
  res.json({
    message: 'Image upload endpoint',
    status: 'not implemented yet'
  });
});

// 错误处理中间件
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: err.message
  });
});

// 404处理
app.use('*', (req: Request, res: Response) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Server is running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
});
