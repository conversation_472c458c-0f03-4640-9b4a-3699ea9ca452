{"name": "img-proc-tools-server", "version": "1.0.0", "description": "Backend API server for Image Processing Tools", "main": "index.ts", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec ts-node index.ts", "build": "tsc && npm run copy-assets", "build:watch": "tsc --watch", "copy-assets": "cp -r src/config dist/src/ && cp -r src/comfyui dist/src/", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["image-processing", "api", "express", "nodejs", "typescript"], "author": "", "license": "ISC", "dependencies": {"@types/multer": "^2.0.0", "axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "form-data": "^4.0.4", "multer": "^2.0.2", "sharp": "^0.34.3"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/dotenv": "^8.2.3", "@types/express": "^5.0.3", "@types/node": "^24.0.15", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}