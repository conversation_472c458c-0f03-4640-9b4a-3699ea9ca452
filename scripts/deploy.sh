#!/bin/bash

# 图像处理工具自动化部署脚本
# 使用方法: ./deploy.sh [环境] [分支]
# 示例: ./deploy.sh production main

set -e

# 配置变量
PROJECT_NAME="img-proc-tools"
PROJECT_DIR="/var/www/$PROJECT_NAME"
BACKUP_DIR="/var/backups/$PROJECT_NAME"
LOG_FILE="/var/log/deploy-$PROJECT_NAME.log"
DATE=$(date +%Y%m%d_%H%M%S)

# 默认参数
ENVIRONMENT=${1:-production}
BRANCH=${2:-main}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a $LOG_FILE
}

# 检查权限
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        error "请不要使用 root 用户运行此脚本"
    fi
    
    if ! sudo -n true 2>/dev/null; then
        error "需要 sudo 权限，请确保当前用户在 sudoers 中"
    fi
}

# 检查依赖
check_dependencies() {
    log "检查系统依赖..."
    
    local deps=("node" "npm" "pm2" "nginx" "git")
    for dep in "${deps[@]}"; do
        if ! command -v $dep &> /dev/null; then
            error "$dep 未安装，请先安装必要依赖"
        fi
    done
    
    # 检查 Node.js 版本
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt 16 ]]; then
        error "Node.js 版本过低，需要 v16.0.0 或更高版本"
    fi
    
    info "依赖检查通过"
}

# 创建备份
create_backup() {
    if [[ -d $PROJECT_DIR ]]; then
        log "创建项目备份..."
        sudo mkdir -p $BACKUP_DIR
        sudo cp -r $PROJECT_DIR $BACKUP_DIR/backup_$DATE
        info "备份创建完成: $BACKUP_DIR/backup_$DATE"
    fi
}

# 清理旧备份
cleanup_backups() {
    log "清理旧备份..."
    sudo find $BACKUP_DIR -name "backup_*" -type d -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    info "旧备份清理完成"
}

# 部署代码
deploy_code() {
    log "开始部署代码..."
    
    # 创建项目目录
    sudo mkdir -p $PROJECT_DIR
    sudo chown -R $USER:$USER $PROJECT_DIR
    
    # 进入项目目录
    cd $PROJECT_DIR
    
    # 克隆或更新代码
    if [[ -d .git ]]; then
        log "更新现有代码库..."
        git fetch origin
        git checkout $BRANCH
        git pull origin $BRANCH
    else
        log "克隆代码库..."
        git clone -b $BRANCH https://gitee.com/web-accelerator/img-proc-tools.git .
    fi
    
    info "代码部署完成"
}

# 安装依赖
install_dependencies() {
    log "安装项目依赖..."
    
    cd $PROJECT_DIR
    
    # 清理 node_modules
    rm -rf node_modules client/node_modules server/node_modules
    
    # 安装根目录依赖
    npm ci --production=false
    
    # 安装前端依赖
    cd client
    npm ci --production=false
    cd ..
    
    # 安装后端依赖
    cd server
    npm ci --production=false
    cd ..
    
    info "依赖安装完成"
}

# 构建项目
build_project() {
    log "构建项目..."
    
    cd $PROJECT_DIR
    
    # 构建前端
    log "构建前端应用..."
    cd client
    npm run build
    cd ..
    
    # 构建后端
    log "构建后端应用..."
    cd server
    npm run build
    cd ..
    
    info "项目构建完成"
}

# 配置环境
setup_environment() {
    log "配置运行环境..."
    
    cd $PROJECT_DIR/server
    
    # 创建环境变量文件
    if [[ ! -f .env ]]; then
        cat > .env << EOF
# 服务器配置
PORT=3011
NODE_ENV=$ENVIRONMENT

# ComfyUI 配置
COMFYUI_API_URL=http://localhost:8188
COMFYUI_HOST=localhost
COMFYUI_PORT=8188

# 文件配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
RESULTS_DIR=results

# 其他配置
API_TIMEOUT=300000
EOF
        info "环境变量文件已创建"
    else
        info "环境变量文件已存在，跳过创建"
    fi
    
    # 创建必要目录
    mkdir -p uploads results logs
    chmod 755 uploads results logs
    
    info "环境配置完成"
}

# 配置 PM2
setup_pm2() {
    log "配置 PM2 进程管理..."
    
    cd $PROJECT_DIR
    
    # 创建 PM2 配置文件
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: '$PROJECT_NAME-server',
      script: './server/dist/index.js',
      cwd: '$PROJECT_DIR',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: '$ENVIRONMENT',
        PORT: 3011
      },
      error_file: './server/logs/err.log',
      out_file: './server/logs/out.log',
      log_file: './server/logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads', 'results']
    }
  ]
};
EOF
    
    info "PM2 配置完成"
}

# 启动服务
start_services() {
    log "启动应用服务..."
    
    cd $PROJECT_DIR
    
    # 停止现有进程
    pm2 delete $PROJECT_NAME-server 2>/dev/null || true
    
    # 启动新进程
    pm2 start ecosystem.config.js
    
    # 保存 PM2 配置
    pm2 save
    
    # 设置开机自启
    pm2 startup | grep -E '^sudo' | bash || true
    
    info "应用服务启动完成"
}

# 配置 Nginx
setup_nginx() {
    log "配置 Nginx..."
    
    local nginx_config="/etc/nginx/sites-available/$PROJECT_NAME"
    local domain=${DOMAIN:-"localhost"}
    
    sudo tee $nginx_config > /dev/null << EOF
server {
    listen 80;
    server_name $domain;
    
    client_max_body_size 10M;
    
    # 前端静态文件
    location / {
        root $PROJECT_DIR/client/dist;
        try_files \$uri \$uri/ /index.html;
        
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:3011;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # 文件服务
    location ~ ^/(uploads|results)/ {
        proxy_pass http://localhost:3011;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    access_log /var/log/nginx/$PROJECT_NAME.access.log;
    error_log /var/log/nginx/$PROJECT_NAME.error.log;
}
EOF
    
    # 启用站点
    sudo ln -sf $nginx_config /etc/nginx/sites-enabled/
    
    # 测试配置
    sudo nginx -t
    
    # 重载 Nginx
    sudo systemctl reload nginx
    
    info "Nginx 配置完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:3011 >/dev/null 2>&1; then
            info "后端服务健康检查通过"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error "后端服务健康检查失败"
        fi
        
        warning "等待后端服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    # 检查前端
    if curl -f http://localhost >/dev/null 2>&1; then
        info "前端服务健康检查通过"
    else
        warning "前端服务可能存在问题"
    fi
}

# 显示部署信息
show_deployment_info() {
    log "部署完成！"
    echo
    echo "=========================================="
    echo "  图像处理工具部署信息"
    echo "=========================================="
    echo "环境: $ENVIRONMENT"
    echo "分支: $BRANCH"
    echo "项目目录: $PROJECT_DIR"
    echo "前端地址: http://localhost"
    echo "后端API: http://localhost/api"
    echo "=========================================="
    echo
    echo "常用命令:"
    echo "  查看服务状态: pm2 status"
    echo "  查看日志: pm2 logs $PROJECT_NAME-server"
    echo "  重启服务: pm2 restart $PROJECT_NAME-server"
    echo "  重新部署: $0 $ENVIRONMENT $BRANCH"
    echo "=========================================="
}

# 主函数
main() {
    log "开始部署 $PROJECT_NAME ($ENVIRONMENT 环境, $BRANCH 分支)"
    
    check_permissions
    check_dependencies
    create_backup
    deploy_code
    install_dependencies
    build_project
    setup_environment
    setup_pm2
    start_services
    setup_nginx
    health_check
    cleanup_backups
    show_deployment_info
    
    log "部署流程全部完成！"
}

# 错误处理
trap 'error "部署过程中发生错误，请检查日志: $LOG_FILE"' ERR

# 执行主函数
main "$@"
