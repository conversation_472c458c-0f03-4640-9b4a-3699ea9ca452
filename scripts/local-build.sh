#!/bin/bash

# 本地构建脚本 - 在本地环境构建并打包部署文件
# 使用方法: ./local-build.sh [版本号]

set -e

PROJECT_NAME="img-proc-tools"
VERSION=${1:-$(date +%Y%m%d_%H%M%S)}
DEPLOY_DIR="deploy-package"
PACKAGE_NAME="${PROJECT_NAME}-${VERSION}.tar.gz"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查本地环境
check_environment() {
    log "检查本地构建环境..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js 未安装，请先安装 Node.js 16+"
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt 16 ]]; then
        error "Node.js 版本过低，需要 v16.0.0 或更高版本，当前版本: $(node -v)"
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        error "npm 未安装"
    fi
    
    # 检查项目文件
    if [[ ! -f "package.json" ]]; then
        error "请在项目根目录运行此脚本"
    fi
    
    if [[ ! -d "client" ]] || [[ ! -d "server" ]]; then
        error "项目结构不正确，缺少 client 或 server 目录"
    fi
    
    info "环境检查通过 - Node.js $(node -v), npm $(npm -v)"
}

# 清理构建目录
clean_build() {
    log "清理构建目录..."
    
    # 清理旧的部署包
    rm -rf $DEPLOY_DIR
    rm -f ${PROJECT_NAME}-*.tar.gz
    
    # 清理前端构建目录
    if [[ -d "client/dist" ]]; then
        rm -rf client/dist
        info "清理前端构建目录"
    fi
    
    # 清理后端构建目录
    if [[ -d "server/dist" ]]; then
        rm -rf server/dist
        info "清理后端构建目录"
    fi
    
    info "构建目录清理完成"
}

# 安装依赖
install_dependencies() {
    log "安装项目依赖..."
    
    # 安装根目录依赖
    info "安装根目录依赖..."
    npm install
    
    # 安装前端依赖
    info "安装前端依赖..."
    cd client
    npm install
    cd ..
    
    # 安装后端依赖
    info "安装后端依赖..."
    cd server
    npm install
    cd ..
    
    info "依赖安装完成"
}

# 构建前端
build_frontend() {
    log "构建前端应用..."
    
    cd client
    
    # 设置生产环境
    export NODE_ENV=production
    
    # 执行构建
    npm run build
    
    # 检查构建结果
    if [[ ! -d "dist" ]]; then
        error "前端构建失败，dist 目录不存在"
    fi
    
    if [[ ! -f "dist/index.html" ]]; then
        error "前端构建失败，index.html 不存在"
    fi
    
    # 显示构建信息
    local build_size=$(du -sh dist | cut -f1)
    local file_count=$(find dist -type f | wc -l)
    info "前端构建完成 - 大小: $build_size, 文件数: $file_count"
    
    cd ..
}

# 构建后端
build_backend() {
    log "构建后端应用..."
    
    cd server
    
    # 执行 TypeScript 编译
    npm run build
    
    # 检查构建结果
    if [[ ! -d "dist" ]]; then
        error "后端构建失败，dist 目录不存在"
    fi
    
    if [[ ! -f "dist/index.js" ]]; then
        error "后端构建失败，index.js 不存在"
    fi
    
    # 显示构建信息
    local build_size=$(du -sh dist | cut -f1)
    local file_count=$(find dist -type f | wc -l)
    info "后端构建完成 - 大小: $build_size, 文件数: $file_count"
    
    cd ..
}

# 创建部署包
create_deploy_package() {
    log "创建部署包..."
    
    # 创建部署目录
    mkdir -p $DEPLOY_DIR
    
    # 复制前端构建文件
    info "复制前端文件..."
    cp -r client/dist $DEPLOY_DIR/frontend
    
    # 复制后端构建文件
    info "复制后端文件..."
    mkdir -p $DEPLOY_DIR/backend
    cp -r server/dist $DEPLOY_DIR/backend/
    cp server/package.json $DEPLOY_DIR/backend/
    cp server/package-lock.json $DEPLOY_DIR/backend/ 2>/dev/null || true
    
    # 复制配置文件
    if [[ -d "server/src/config" ]]; then
        mkdir -p $DEPLOY_DIR/backend/src
        cp -r server/src/config $DEPLOY_DIR/backend/src/
        info "复制配置文件"
    fi
    
    # 创建 PM2 配置文件
    cat > $DEPLOY_DIR/ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: '${PROJECT_NAME}-server',
      script: './backend/dist/index.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3011
      },
      error_file: './backend/logs/err.log',
      out_file: './backend/logs/out.log',
      log_file: './backend/logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      autorestart: true
    }
  ]
};
EOF
    
    # 创建环境变量模板
    cat > $DEPLOY_DIR/backend/.env.example << EOF
# 服务器配置
PORT=3011
NODE_ENV=production

# ComfyUI 配置
COMFYUI_API_URL=http://localhost:8188
COMFYUI_HOST=localhost
COMFYUI_PORT=8188

# 文件配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
RESULTS_DIR=results

# 其他配置
API_TIMEOUT=300000
EOF
    
    # 创建部署说明文件
    cat > $DEPLOY_DIR/README.md << EOF
# ${PROJECT_NAME} 部署包

**构建信息:**
- 构建时间: $(date)
- 构建版本: ${VERSION}
- Node.js 版本: $(node -v)

## 目录结构
\`\`\`
├── frontend/              # 前端静态文件 (Nginx 服务)
├── backend/               # 后端应用文件 (PM2 管理)
│   ├── dist/             # 编译后的 JavaScript 文件
│   ├── package.json      # 生产依赖配置
│   ├── .env.example      # 环境变量模板
│   └── logs/             # 日志目录 (运行时创建)
└── ecosystem.config.js    # PM2 配置文件
\`\`\`

## 服务器部署步骤

### 1. 上传部署包
\`\`\`bash
scp ${PACKAGE_NAME} user@server:/tmp/
\`\`\`

### 2. 服务器端部署
\`\`\`bash
# 登录服务器
ssh user@server

# 解压到项目目录
sudo mkdir -p /var/www/${PROJECT_NAME}
sudo chown -R \$USER:\$USER /var/www/${PROJECT_NAME}
cd /var/www/${PROJECT_NAME}
tar -xzf /tmp/${PACKAGE_NAME}

# 安装后端依赖
cd backend
npm ci --only=production

# 创建必要目录和配置
mkdir -p uploads results logs
cp .env.example .env
# 编辑 .env 文件配置实际参数

# 启动服务
cd ..
pm2 start ecosystem.config.js
pm2 save
\`\`\`

### 3. 配置 Nginx
参考项目文档中的 Nginx 配置，将前端指向 \`frontend/\` 目录。

### 4. 验证部署
\`\`\`bash
curl http://localhost:3011  # 后端 API
curl http://localhost:5183  # 前端页面
pm2 status                  # 服务状态
\`\`\`

## 管理命令
\`\`\`bash
pm2 status                    # 查看服务状态
pm2 restart ${PROJECT_NAME}-server  # 重启服务
pm2 logs ${PROJECT_NAME}-server     # 查看日志
pm2 monit                     # 监控资源使用
\`\`\`
EOF
    
    info "部署包创建完成"
}

# 打包文件
package_files() {
    log "打包部署文件..."
    
    # 创建压缩包
    tar -czf $PACKAGE_NAME -C $DEPLOY_DIR .
    
    # 获取包信息
    local package_size=$(du -sh $PACKAGE_NAME | cut -f1)
    local frontend_size=$(du -sh $DEPLOY_DIR/frontend | cut -f1)
    local backend_size=$(du -sh $DEPLOY_DIR/backend | cut -f1)
    
    info "打包完成 - 总大小: $package_size"
    
    # 显示详细信息
    echo
    echo "=========================================="
    echo "  构建完成信息"
    echo "=========================================="
    echo "构建版本: $VERSION"
    echo "构建时间: $(date)"
    echo "部署包名: $PACKAGE_NAME"
    echo "包大小: $package_size"
    echo "前端大小: $frontend_size"
    echo "后端大小: $backend_size"
    echo "=========================================="
    echo
    echo "部署命令:"
    echo "1. 上传: scp $PACKAGE_NAME user@server:/tmp/"
    echo "2. 部署: 参考 $DEPLOY_DIR/README.md"
    echo "=========================================="
}

# 显示构建统计
show_build_stats() {
    echo
    echo "=========================================="
    echo "  构建统计"
    echo "=========================================="
    
    if [[ -d "client/dist" ]]; then
        local frontend_files=$(find client/dist -type f | wc -l)
        echo "前端文件数: $frontend_files"
    fi
    
    if [[ -d "server/dist" ]]; then
        local backend_files=$(find server/dist -type f | wc -l)
        echo "后端文件数: $backend_files"
    fi
    
    if [[ -f "$PACKAGE_NAME" ]]; then
        echo "部署包: $PACKAGE_NAME"
        echo "MD5: $(md5sum $PACKAGE_NAME | cut -d' ' -f1)"
    fi
    
    echo "=========================================="
}

# 主函数
main() {
    log "开始本地构建 $PROJECT_NAME (版本: $VERSION)"
    
    check_environment
    clean_build
    install_dependencies
    build_frontend
    build_backend
    create_deploy_package
    package_files
    show_build_stats
    
    log "本地构建完成！"
    echo
    echo "🎉 部署包已准备就绪: $PACKAGE_NAME"
    echo "📖 部署说明: $DEPLOY_DIR/README.md"
}

# 错误处理
trap 'error "构建过程中发生错误"' ERR

# 执行主函数
main "$@"
