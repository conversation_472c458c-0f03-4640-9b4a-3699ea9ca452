#!/bin/bash

# 生产环境构建脚本
# 用于构建前端静态文件和后端编译文件

set -e

PROJECT_NAME="img-proc-tools"
BUILD_DIR="build"
DATE=$(date +%Y%m%d_%H%M%S)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查环境
check_environment() {
    log "检查构建环境..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js 未安装"
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt 16 ]]; then
        error "Node.js 版本过低，需要 v16.0.0 或更高版本"
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        error "npm 未安装"
    fi
    
    info "环境检查通过 - Node.js $(node -v), npm $(npm -v)"
}

# 清理构建目录
clean_build() {
    log "清理构建目录..."
    
    # 清理前端构建目录
    if [[ -d "client/dist" ]]; then
        rm -rf client/dist
        info "清理前端构建目录"
    fi
    
    # 清理后端构建目录
    if [[ -d "server/dist" ]]; then
        rm -rf server/dist
        info "清理后端构建目录"
    fi
    
    # 清理根目录构建目录
    if [[ -d "$BUILD_DIR" ]]; then
        rm -rf "$BUILD_DIR"
        info "清理根构建目录"
    fi
}

# 安装依赖
install_dependencies() {
    log "安装项目依赖..."
    
    # 安装根目录依赖
    info "安装根目录依赖..."
    npm ci
    
    # 安装前端依赖
    info "安装前端依赖..."
    cd client
    npm ci
    cd ..
    
    # 安装后端依赖
    info "安装后端依赖..."
    cd server
    npm ci
    cd ..
    
    info "依赖安装完成"
}

# 构建前端
build_frontend() {
    log "构建前端应用..."
    
    cd client
    
    # 设置生产环境变量
    export NODE_ENV=production
    
    # 执行构建
    npm run build
    
    # 检查构建结果
    if [[ ! -d "dist" ]]; then
        error "前端构建失败，dist 目录不存在"
    fi
    
    if [[ ! -f "dist/index.html" ]]; then
        error "前端构建失败，index.html 不存在"
    fi
    
    # 显示构建信息
    local build_size=$(du -sh dist | cut -f1)
    info "前端构建完成，大小: $build_size"
    
    # 列出构建文件
    echo "构建文件列表:"
    ls -la dist/
    
    cd ..
}

# 构建后端
build_backend() {
    log "构建后端应用..."
    
    cd server
    
    # 执行 TypeScript 编译
    npm run build
    
    # 检查构建结果
    if [[ ! -d "dist" ]]; then
        error "后端构建失败，dist 目录不存在"
    fi
    
    if [[ ! -f "dist/index.js" ]]; then
        error "后端构建失败，index.js 不存在"
    fi
    
    # 显示构建信息
    local build_size=$(du -sh dist | cut -f1)
    info "后端构建完成，大小: $build_size"
    
    # 列出构建文件
    echo "构建文件列表:"
    ls -la dist/
    
    cd ..
}

# 创建生产环境包
create_production_package() {
    log "创建生产环境部署包..."
    
    mkdir -p "$BUILD_DIR"
    
    # 复制前端构建文件
    info "复制前端构建文件..."
    cp -r client/dist "$BUILD_DIR/frontend"
    
    # 复制后端构建文件
    info "复制后端构建文件..."
    mkdir -p "$BUILD_DIR/backend"
    cp -r server/dist "$BUILD_DIR/backend/"
    cp server/package.json "$BUILD_DIR/backend/"
    cp server/package-lock.json "$BUILD_DIR/backend/" 2>/dev/null || true
    
    # 复制配置文件
    if [[ -d "server/src/config" ]]; then
        cp -r server/src/config "$BUILD_DIR/backend/src/"
    fi
    
    # 创建环境变量模板
    cat > "$BUILD_DIR/backend/.env.example" << EOF
# 服务器配置
PORT=3011
NODE_ENV=production

# ComfyUI 配置
COMFYUI_API_URL=http://localhost:8188
COMFYUI_HOST=localhost
COMFYUI_PORT=8188

# 文件配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
RESULTS_DIR=results

# 其他配置
API_TIMEOUT=300000
EOF
    
    # 创建 PM2 配置文件
    cat > "$BUILD_DIR/ecosystem.config.js" << EOF
module.exports = {
  apps: [
    {
      name: '$PROJECT_NAME-server',
      script: './backend/dist/index.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3011
      },
      error_file: './backend/logs/err.log',
      out_file: './backend/logs/out.log',
      log_file: './backend/logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      autorestart: true
    }
  ]
};
EOF
    
    # 创建部署说明文件
    cat > "$BUILD_DIR/README.md" << EOF
# $PROJECT_NAME 生产环境部署包

构建时间: $(date)
构建版本: $DATE

## 目录结构
- frontend/     前端静态文件 (由 Nginx 服务)
- backend/      后端应用文件 (由 PM2 管理)
- ecosystem.config.js  PM2 配置文件

## 部署步骤

1. 上传整个目录到服务器 /var/www/$PROJECT_NAME/

2. 安装后端生产依赖:
   cd backend && npm ci --only=production

3. 创建必要目录:
   mkdir -p uploads results logs

4. 配置环境变量:
   cp .env.example .env
   # 编辑 .env 文件

5. 启动服务:
   pm2 start ../ecosystem.config.js

6. 配置 Nginx 指向 frontend/ 目录

## 验证部署
curl http://localhost:3011  # 后端 API
curl http://localhost       # 前端页面
EOF
    
    info "生产环境包创建完成: $BUILD_DIR/"
}

# 打包部署文件
package_build() {
    log "打包部署文件..."
    
    local package_name="${PROJECT_NAME}-${DATE}.tar.gz"
    
    tar -czf "$package_name" -C "$BUILD_DIR" .
    
    local package_size=$(du -sh "$package_name" | cut -f1)
    info "部署包创建完成: $package_name (大小: $package_size)"
    
    echo
    echo "=========================================="
    echo "  构建完成信息"
    echo "=========================================="
    echo "构建时间: $(date)"
    echo "部署包: $package_name"
    echo "包大小: $package_size"
    echo "前端文件: $BUILD_DIR/frontend/"
    echo "后端文件: $BUILD_DIR/backend/"
    echo "PM2配置: $BUILD_DIR/ecosystem.config.js"
    echo "=========================================="
    echo
    echo "部署命令:"
    echo "1. 上传到服务器: scp $package_name user@server:/tmp/"
    echo "2. 解压: tar -xzf /tmp/$package_name -C /var/www/$PROJECT_NAME/"
    echo "3. 安装依赖: cd /var/www/$PROJECT_NAME/backend && npm ci --only=production"
    echo "4. 启动服务: pm2 start /var/www/$PROJECT_NAME/ecosystem.config.js"
    echo "=========================================="
}

# 显示构建统计
show_build_stats() {
    log "构建统计信息..."
    
    echo
    echo "=========================================="
    echo "  构建统计"
    echo "=========================================="
    
    if [[ -d "client/dist" ]]; then
        local frontend_size=$(du -sh client/dist | cut -f1)
        local frontend_files=$(find client/dist -type f | wc -l)
        echo "前端: $frontend_size ($frontend_files 个文件)"
    fi
    
    if [[ -d "server/dist" ]]; then
        local backend_size=$(du -sh server/dist | cut -f1)
        local backend_files=$(find server/dist -type f | wc -l)
        echo "后端: $backend_size ($backend_files 个文件)"
    fi
    
    if [[ -d "$BUILD_DIR" ]]; then
        local total_size=$(du -sh "$BUILD_DIR" | cut -f1)
        echo "总计: $total_size"
    fi
    
    echo "=========================================="
}

# 主函数
main() {
    local action=${1:-all}
    
    log "开始构建 $PROJECT_NAME 生产环境"
    
    case $action in
        clean)
            clean_build
            ;;
        deps)
            install_dependencies
            ;;
        frontend)
            build_frontend
            ;;
        backend)
            build_backend
            ;;
        package)
            create_production_package
            package_build
            ;;
        all)
            check_environment
            clean_build
            install_dependencies
            build_frontend
            build_backend
            create_production_package
            package_build
            show_build_stats
            ;;
        *)
            echo "用法: $0 [clean|deps|frontend|backend|package|all]"
            exit 1
            ;;
    esac
    
    log "构建完成！"
}

# 错误处理
trap 'error "构建过程中发生错误"' ERR

# 执行主函数
main "$@"
