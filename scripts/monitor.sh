#!/bin/bash

# 图像处理工具监控脚本
# 使用方法: ./monitor.sh [选项]
# 选项: status, logs, resources, health

set -e

PROJECT_NAME="img-proc-tools"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 显示帮助信息
show_help() {
    echo "图像处理工具监控脚本"
    echo
    echo "使用方法: $0 [选项]"
    echo
    echo "选项:"
    echo "  status     显示服务状态"
    echo "  logs       显示应用日志"
    echo "  resources  显示系统资源使用情况"
    echo "  health     执行健康检查"
    echo "  all        显示所有信息"
    echo "  help       显示此帮助信息"
    echo
}

# 显示服务状态
show_status() {
    echo -e "${BLUE}=========================================="
    echo -e "  服务状态信息"
    echo -e "==========================================${NC}"
    
    # PM2 状态
    echo -e "${GREEN}PM2 进程状态:${NC}"
    pm2 list
    echo
    
    # Nginx 状态
    echo -e "${GREEN}Nginx 状态:${NC}"
    sudo systemctl status nginx --no-pager -l | head -20
    echo
    
    # 端口占用
    echo -e "${GREEN}端口占用情况:${NC}"
    echo -e "${CYAN}端口 80 (HTTP):${NC}"
    sudo netstat -tlnp | grep ':80 ' || echo "未占用"
    echo -e "${CYAN}端口 443 (HTTPS):${NC}"
    sudo netstat -tlnp | grep ':443 ' || echo "未占用"
    echo -e "${CYAN}端口 3011 (后端API):${NC}"
    sudo netstat -tlnp | grep ':3011 ' || echo "未占用"
    echo
}

# 显示应用日志
show_logs() {
    echo -e "${BLUE}=========================================="
    echo -e "  应用日志信息"
    echo -e "==========================================${NC}"
    
    # PM2 日志
    echo -e "${GREEN}PM2 应用日志 (最近50行):${NC}"
    pm2 logs "$PROJECT_NAME-server" --lines 50 --nostream
    echo
    
    # Nginx 访问日志
    echo -e "${GREEN}Nginx 访问日志 (最近20行):${NC}"
    if [[ -f "/var/log/nginx/$PROJECT_NAME.access.log" ]]; then
        sudo tail -20 "/var/log/nginx/$PROJECT_NAME.access.log"
    else
        echo "访问日志文件不存在"
    fi
    echo
    
    # Nginx 错误日志
    echo -e "${GREEN}Nginx 错误日志 (最近20行):${NC}"
    if [[ -f "/var/log/nginx/$PROJECT_NAME.error.log" ]]; then
        sudo tail -20 "/var/log/nginx/$PROJECT_NAME.error.log"
    else
        echo "错误日志文件不存在"
    fi
    echo
}

# 显示系统资源
show_resources() {
    echo -e "${BLUE}=========================================="
    echo -e "  系统资源使用情况"
    echo -e "==========================================${NC}"
    
    # CPU 和内存
    echo -e "${GREEN}CPU 和内存使用情况:${NC}"
    top -bn1 | head -5
    echo
    
    # 磁盘使用
    echo -e "${GREEN}磁盘使用情况:${NC}"
    df -h
    echo
    
    # 内存详情
    echo -e "${GREEN}内存详情:${NC}"
    free -h
    echo
    
    # 负载平均值
    echo -e "${GREEN}系统负载:${NC}"
    uptime
    echo
    
    # PM2 监控
    echo -e "${GREEN}PM2 进程资源使用:${NC}"
    pm2 monit --no-interaction | head -20
    echo
}

# 健康检查
health_check() {
    echo -e "${BLUE}=========================================="
    echo -e "  健康检查"
    echo -e "==========================================${NC}"
    
    local all_healthy=true
    
    # 检查后端 API
    echo -e "${GREEN}检查后端 API...${NC}"
    if curl -f -s http://localhost:3011 >/dev/null; then
        echo -e "${GREEN}✓ 后端 API 正常${NC}"
    else
        echo -e "${RED}✗ 后端 API 异常${NC}"
        all_healthy=false
    fi
    
    # 检查前端
    echo -e "${GREEN}检查前端服务...${NC}"
    if curl -f -s http://localhost >/dev/null; then
        echo -e "${GREEN}✓ 前端服务正常${NC}"
    else
        echo -e "${RED}✗ 前端服务异常${NC}"
        all_healthy=false
    fi
    
    # 检查 PM2 进程
    echo -e "${GREEN}检查 PM2 进程...${NC}"
    if pm2 list | grep -q "$PROJECT_NAME-server.*online"; then
        echo -e "${GREEN}✓ PM2 进程正常${NC}"
    else
        echo -e "${RED}✗ PM2 进程异常${NC}"
        all_healthy=false
    fi
    
    # 检查 Nginx
    echo -e "${GREEN}检查 Nginx 服务...${NC}"
    if sudo systemctl is-active nginx >/dev/null; then
        echo -e "${GREEN}✓ Nginx 服务正常${NC}"
    else
        echo -e "${RED}✗ Nginx 服务异常${NC}"
        all_healthy=false
    fi
    
    # 检查磁盘空间
    echo -e "${GREEN}检查磁盘空间...${NC}"
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $disk_usage -lt 90 ]]; then
        echo -e "${GREEN}✓ 磁盘空间充足 (${disk_usage}% 已使用)${NC}"
    else
        echo -e "${RED}✗ 磁盘空间不足 (${disk_usage}% 已使用)${NC}"
        all_healthy=false
    fi
    
    # 检查内存使用
    echo -e "${GREEN}检查内存使用...${NC}"
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [[ $mem_usage -lt 90 ]]; then
        echo -e "${GREEN}✓ 内存使用正常 (${mem_usage}% 已使用)${NC}"
    else
        echo -e "${YELLOW}⚠ 内存使用较高 (${mem_usage}% 已使用)${NC}"
    fi
    
    echo
    if $all_healthy; then
        echo -e "${GREEN}🎉 所有服务运行正常！${NC}"
    else
        echo -e "${RED}❌ 发现服务异常，请检查相关日志${NC}"
    fi
    echo
}

# 显示实时监控
show_realtime() {
    echo -e "${BLUE}=========================================="
    echo -e "  实时监控 (按 Ctrl+C 退出)"
    echo -e "==========================================${NC}"
    
    while true; do
        clear
        echo -e "${PURPLE}图像处理工具实时监控 - $(date)${NC}"
        echo
        
        # 简化的状态信息
        echo -e "${GREEN}服务状态:${NC}"
        pm2 list | grep "$PROJECT_NAME-server" || echo "服务未运行"
        echo
        
        echo -e "${GREEN}系统资源:${NC}"
        echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
        echo "内存: $(free | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
        echo "磁盘: $(df / | awk 'NR==2 {print $5}')"
        echo
        
        echo -e "${GREEN}最新日志:${NC}"
        pm2 logs "$PROJECT_NAME-server" --lines 5 --nostream 2>/dev/null | tail -5
        
        sleep 5
    done
}

# 主函数
main() {
    local action=${1:-help}
    
    case $action in
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        resources)
            show_resources
            ;;
        health)
            health_check
            ;;
        all)
            show_status
            echo
            show_resources
            echo
            health_check
            ;;
        realtime)
            show_realtime
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
