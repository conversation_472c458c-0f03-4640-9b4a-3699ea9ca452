#!/bin/bash

# 图像处理工具一键部署脚本
# 适用于全新的 Ubuntu/Debian 服务器

set -e

PROJECT_NAME="img-proc-tools"
PROJECT_DIR="/var/www/$PROJECT_NAME"
DOMAIN=${1:-"localhost"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${BLUE}"
    echo "=========================================="
    echo "  图像处理工具一键部署脚本"
    echo "=========================================="
    echo -e "${NC}"
    echo "本脚本将自动完成以下操作："
    echo "1. 安装系统依赖 (Node.js, Nginx, PM2)"
    echo "2. 克隆项目代码"
    echo "3. 安装项目依赖"
    echo "4. 构建前后端应用"
    echo "5. 配置 PM2 和 Nginx"
    echo "6. 启动服务"
    echo
    echo "域名/IP: $DOMAIN"
    echo "项目目录: $PROJECT_DIR"
    echo
    read -p "按 Enter 继续，或 Ctrl+C 取消..."
}

# 检查系统
check_system() {
    step "检查系统环境"
    
    # 检查操作系统
    if [[ ! -f /etc/debian_version ]]; then
        error "此脚本仅支持 Ubuntu/Debian 系统"
    fi
    
    # 检查权限
    if [[ $EUID -eq 0 ]]; then
        error "请不要使用 root 用户运行此脚本"
    fi
    
    # 检查 sudo 权限
    if ! sudo -n true 2>/dev/null; then
        error "需要 sudo 权限，请确保当前用户在 sudoers 中"
    fi
    
    info "系统检查通过"
}

# 更新系统
update_system() {
    step "更新系统包"
    
    sudo apt update
    sudo apt upgrade -y
    sudo apt install -y curl wget git vim unzip build-essential
    
    info "系统更新完成"
}

# 安装 Node.js
install_nodejs() {
    step "安装 Node.js"
    
    if command -v node &> /dev/null; then
        local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
        if [[ $node_version -ge 16 ]]; then
            info "Node.js 已安装: $(node -v)"
            return
        fi
    fi
    
    # 安装 Node.js 18
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    # 验证安装
    node --version
    npm --version
    
    info "Node.js 安装完成: $(node -v)"
}

# 安装 PM2
install_pm2() {
    step "安装 PM2"
    
    if command -v pm2 &> /dev/null; then
        info "PM2 已安装: $(pm2 -v)"
        return
    fi
    
    sudo npm install -g pm2
    
    info "PM2 安装完成: $(pm2 -v)"
}

# 安装 Nginx
install_nginx() {
    step "安装 Nginx"
    
    if command -v nginx &> /dev/null; then
        info "Nginx 已安装"
        return
    fi
    
    sudo apt install -y nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    
    info "Nginx 安装完成"
}

# 克隆项目
clone_project() {
    step "克隆项目代码"
    
    # 创建项目目录
    sudo mkdir -p $PROJECT_DIR
    sudo chown -R $USER:$USER $PROJECT_DIR
    
    # 克隆代码
    if [[ -d "$PROJECT_DIR/.git" ]]; then
        info "项目已存在，更新代码..."
        cd $PROJECT_DIR
        git pull origin main
    else
        info "克隆新项目..."
        git clone https://gitee.com/web-accelerator/img-proc-tools.git $PROJECT_DIR
        cd $PROJECT_DIR
    fi
    
    info "项目代码准备完成"
}

# 安装项目依赖
install_dependencies() {
    step "安装项目依赖"
    
    cd $PROJECT_DIR
    
    # 安装根目录依赖
    info "安装根目录依赖..."
    npm install
    
    # 安装前端依赖
    info "安装前端依赖..."
    cd client
    npm install
    cd ..
    
    # 安装后端依赖
    info "安装后端依赖..."
    cd server
    npm install
    cd ..
    
    info "依赖安装完成"
}

# 构建项目
build_project() {
    step "构建项目"
    
    cd $PROJECT_DIR
    
    # 构建前端
    info "构建前端..."
    cd client
    npm run build
    cd ..
    
    # 构建后端
    info "构建后端..."
    cd server
    npm run build
    cd ..
    
    info "项目构建完成"
}

# 配置环境
setup_environment() {
    step "配置运行环境"
    
    cd $PROJECT_DIR/server
    
    # 创建环境变量文件
    cat > .env << EOF
# 服务器配置
PORT=3011
NODE_ENV=production

# ComfyUI 配置
COMFYUI_API_URL=http://localhost:8188
COMFYUI_HOST=localhost
COMFYUI_PORT=8188

# 文件配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
RESULTS_DIR=results

# 其他配置
API_TIMEOUT=300000
EOF
    
    # 创建必要目录
    mkdir -p uploads results logs
    chmod 755 uploads results logs
    
    info "环境配置完成"
}

# 配置 PM2
setup_pm2() {
    step "配置 PM2"
    
    cd $PROJECT_DIR
    
    # 创建 PM2 配置文件
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: '$PROJECT_NAME-server',
      script: './server/dist/index.js',
      cwd: '$PROJECT_DIR',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: 3011
      },
      error_file: './server/logs/err.log',
      out_file: './server/logs/out.log',
      log_file: './server/logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      watch: false,
      autorestart: true
    }
  ]
};
EOF
    
    # 启动服务
    pm2 start ecosystem.config.js
    pm2 save
    pm2 startup | grep -E '^sudo' | bash || true
    
    info "PM2 配置完成"
}

# 配置 Nginx
setup_nginx() {
    step "配置 Nginx"
    
    # 创建 Nginx 配置文件
    sudo tee /etc/nginx/sites-available/$PROJECT_NAME > /dev/null << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    client_max_body_size 10M;
    
    # 前端静态文件
    location / {
        root $PROJECT_DIR/client/dist;
        try_files \$uri \$uri/ /index.html;
        
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:3011;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # 文件服务
    location ~ ^/(uploads|results)/ {
        proxy_pass http://localhost:3011;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    access_log /var/log/nginx/$PROJECT_NAME.access.log;
    error_log /var/log/nginx/$PROJECT_NAME.error.log;
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/$PROJECT_NAME /etc/nginx/sites-enabled/
    
    # 删除默认站点
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    sudo nginx -t
    
    # 重载 Nginx
    sudo systemctl reload nginx
    
    info "Nginx 配置完成"
}

# 健康检查
health_check() {
    step "执行健康检查"
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:3011 >/dev/null 2>&1; then
            info "后端服务健康检查通过"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error "后端服务健康检查失败"
        fi
        
        warning "等待后端服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    # 检查前端
    if curl -f http://localhost >/dev/null 2>&1; then
        info "前端服务健康检查通过"
    else
        warning "前端服务可能存在问题"
    fi
}

# 显示部署结果
show_result() {
    clear
    echo -e "${GREEN}"
    echo "=========================================="
    echo "  部署完成！"
    echo "=========================================="
    echo -e "${NC}"
    echo "项目地址: http://$DOMAIN"
    echo "后端API: http://$DOMAIN/api"
    echo "项目目录: $PROJECT_DIR"
    echo
    echo "服务管理命令:"
    echo "  查看状态: pm2 status"
    echo "  查看日志: pm2 logs $PROJECT_NAME-server"
    echo "  重启服务: pm2 restart $PROJECT_NAME-server"
    echo
    echo "Nginx 管理:"
    echo "  重载配置: sudo systemctl reload nginx"
    echo "  查看状态: sudo systemctl status nginx"
    echo
    echo "日志文件:"
    echo "  应用日志: $PROJECT_DIR/server/logs/"
    echo "  Nginx日志: /var/log/nginx/$PROJECT_NAME.*.log"
    echo
    echo -e "${GREEN}🎉 部署成功！请访问 http://$DOMAIN 查看应用${NC}"
}

# 主函数
main() {
    show_welcome
    check_system
    update_system
    install_nodejs
    install_pm2
    install_nginx
    clone_project
    install_dependencies
    build_project
    setup_environment
    setup_pm2
    setup_nginx
    health_check
    show_result
}

# 错误处理
trap 'error "部署过程中发生错误，请检查上面的错误信息"' ERR

# 执行主函数
main "$@"
