#!/bin/bash

# 服务器部署脚本 - 在服务器上部署本地构建的包
# 使用方法: ./server-deploy.sh <部署包路径> [项目目录]

set -e

PACKAGE_FILE=$1
PROJECT_DIR=${2:-"/var/www/img-proc-tools"}
PROJECT_NAME="img-proc-tools"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "服务器部署脚本"
    echo
    echo "使用方法: $0 <部署包路径> [项目目录]"
    echo
    echo "参数:"
    echo "  部署包路径    本地构建生成的 .tar.gz 文件路径"
    echo "  项目目录      服务器上的项目部署目录 (默认: /var/www/img-proc-tools)"
    echo
    echo "示例:"
    echo "  $0 /tmp/img-proc-tools-20240101_120000.tar.gz"
    echo "  $0 ./img-proc-tools-latest.tar.gz /opt/img-proc-tools"
    echo
}

# 检查参数
check_parameters() {
    if [[ -z "$PACKAGE_FILE" ]]; then
        echo "错误: 请提供部署包文件路径"
        echo
        show_help
        exit 1
    fi
    
    if [[ ! -f "$PACKAGE_FILE" ]]; then
        error "部署包文件不存在: $PACKAGE_FILE"
    fi
    
    info "部署包: $PACKAGE_FILE"
    info "目标目录: $PROJECT_DIR"
}

# 检查服务器环境
check_server_environment() {
    log "检查服务器环境..."
    
    # 检查权限
    if [[ $EUID -eq 0 ]]; then
        error "请不要使用 root 用户运行此脚本"
    fi
    
    # 检查 sudo 权限
    if ! sudo -n true 2>/dev/null; then
        error "需要 sudo 权限，请确保当前用户在 sudoers 中"
    fi
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js 未安装，请先安装 Node.js"
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        error "npm 未安装"
    fi
    
    # 检查 PM2
    if ! command -v pm2 &> /dev/null; then
        error "PM2 未安装，请先安装: sudo npm install -g pm2"
    fi
    
    # 检查 Nginx
    if ! command -v nginx &> /dev/null; then
        warning "Nginx 未安装，请手动安装并配置"
    fi
    
    info "服务器环境检查通过"
}

# 备份现有部署
backup_existing() {
    if [[ -d "$PROJECT_DIR" ]]; then
        log "备份现有部署..."
        
        local backup_dir="${PROJECT_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
        sudo cp -r "$PROJECT_DIR" "$backup_dir"
        sudo chown -R $USER:$USER "$backup_dir"
        
        info "备份完成: $backup_dir"
    else
        info "首次部署，无需备份"
    fi
}

# 停止现有服务
stop_services() {
    log "停止现有服务..."
    
    # 停止 PM2 进程
    if pm2 list | grep -q "${PROJECT_NAME}-server"; then
        pm2 stop "${PROJECT_NAME}-server" || true
        pm2 delete "${PROJECT_NAME}-server" || true
        info "已停止现有 PM2 服务"
    else
        info "未发现运行中的 PM2 服务"
    fi
}

# 部署新版本
deploy_new_version() {
    log "部署新版本..."
    
    # 创建项目目录
    sudo mkdir -p "$PROJECT_DIR"
    sudo chown -R $USER:$USER "$PROJECT_DIR"
    
    # 清空目录
    rm -rf "$PROJECT_DIR"/*
    
    # 解压部署包
    info "解压部署包到 $PROJECT_DIR"
    tar -xzf "$PACKAGE_FILE" -C "$PROJECT_DIR"
    
    # 检查解压结果
    if [[ ! -d "$PROJECT_DIR/frontend" ]] || [[ ! -d "$PROJECT_DIR/backend" ]]; then
        error "部署包结构不正确，缺少 frontend 或 backend 目录"
    fi
    
    info "部署包解压完成"
}

# 安装后端依赖
install_backend_dependencies() {
    log "安装后端依赖..."
    
    cd "$PROJECT_DIR/backend"
    
    # 安装生产依赖
    npm ci --only=production
    
    info "后端依赖安装完成"
}

# 配置环境
setup_environment() {
    log "配置运行环境..."
    
    cd "$PROJECT_DIR/backend"
    
    # 创建必要目录
    mkdir -p uploads results logs
    chmod 755 uploads results logs
    
    # 配置环境变量
    if [[ ! -f .env ]]; then
        if [[ -f .env.example ]]; then
            cp .env.example .env
            info "已创建环境变量文件，请编辑 .env 配置实际参数"
        else
            # 创建基本的环境变量文件
            cat > .env << EOF
PORT=3011
NODE_ENV=production
COMFYUI_API_URL=http://localhost:8188
COMFYUI_HOST=localhost
COMFYUI_PORT=8188
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
RESULTS_DIR=results
API_TIMEOUT=300000
EOF
            info "已创建默认环境变量文件"
        fi
    else
        info "环境变量文件已存在，保持现有配置"
    fi
    
    cd "$PROJECT_DIR"
}

# 启动服务
start_services() {
    log "启动服务..."
    
    cd "$PROJECT_DIR"
    
    # 检查 PM2 配置文件
    if [[ ! -f "ecosystem.config.js" ]]; then
        error "PM2 配置文件不存在: ecosystem.config.js"
    fi
    
    # 启动 PM2 服务
    pm2 start ecosystem.config.js
    
    # 保存 PM2 配置
    pm2 save
    
    # 设置开机自启 (如果尚未设置)
    if ! pm2 startup | grep -q "already"; then
        pm2 startup | grep -E '^sudo' | bash || true
    fi
    
    info "服务启动完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    # 等待服务启动
    sleep 3
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s http://localhost:3011 >/dev/null 2>&1; then
            info "后端服务健康检查通过"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error "后端服务健康检查失败，请检查日志: pm2 logs ${PROJECT_NAME}-server"
        fi
        
        warning "等待后端服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    # 检查前端文件
    if [[ -f "$PROJECT_DIR/frontend/index.html" ]]; then
        info "前端文件检查通过"
    else
        warning "前端文件可能存在问题"
    fi
}

# 显示部署信息
show_deployment_info() {
    log "部署完成！"
    
    echo
    echo "=========================================="
    echo "  部署信息"
    echo "=========================================="
    echo "项目目录: $PROJECT_DIR"
    echo "前端文件: $PROJECT_DIR/frontend/"
    echo "后端服务: $PROJECT_DIR/backend/"
    echo "配置文件: $PROJECT_DIR/ecosystem.config.js"
    echo "环境变量: $PROJECT_DIR/backend/.env"
    echo "=========================================="
    echo
    echo "服务状态:"
    pm2 list | grep "${PROJECT_NAME}-server" || echo "服务未运行"
    echo
    echo "访问地址:"
    echo "  后端API: http://localhost:3011"
    echo "  前端页面: 需要配置 Nginx 指向 $PROJECT_DIR/frontend/"
    echo
    echo "管理命令:"
    echo "  查看状态: pm2 status"
    echo "  查看日志: pm2 logs ${PROJECT_NAME}-server"
    echo "  重启服务: pm2 restart ${PROJECT_NAME}-server"
    echo "  停止服务: pm2 stop ${PROJECT_NAME}-server"
    echo "=========================================="
    echo
    echo "Nginx 配置提示:"
    echo "请确保 Nginx 配置中的 root 指向: $PROJECT_DIR/frontend"
    echo "API 代理指向: http://localhost:3011"
    echo
}

# 清理临时文件
cleanup() {
    log "清理临时文件..."
    
    # 清理解压的临时文件等
    # 这里可以添加其他清理逻辑
    
    info "清理完成"
}

# 主函数
main() {
    log "开始服务器部署"
    
    check_parameters
    check_server_environment
    backup_existing
    stop_services
    deploy_new_version
    install_backend_dependencies
    setup_environment
    start_services
    health_check
    show_deployment_info
    cleanup
    
    log "部署流程完成！"
}

# 错误处理
trap 'error "部署过程中发生错误，请检查上面的错误信息"' ERR

# 执行主函数
main "$@"
