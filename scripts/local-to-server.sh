#!/bin/bash

# 本地构建 + 服务器部署一键脚本
# 使用方法: ./local-to-server.sh <服务器地址> [用户名] [项目目录]

set -e

SERVER_HOST=$1
SERVER_USER=${2:-$USER}
PROJECT_DIR=${3:-"/var/www/img-proc-tools"}
PROJECT_NAME="img-proc-tools"
VERSION=$(date +%Y%m%d_%H%M%S)
PACKAGE_NAME="${PROJECT_NAME}-${VERSION}.tar.gz"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "本地构建 + 服务器部署一键脚本"
    echo
    echo "使用方法: $0 <服务器地址> [用户名] [项目目录]"
    echo
    echo "参数:"
    echo "  服务器地址    目标服务器的IP或域名"
    echo "  用户名        SSH登录用户名 (默认: 当前用户)"
    echo "  项目目录      服务器上的项目目录 (默认: /var/www/img-proc-tools)"
    echo
    echo "示例:"
    echo "  $0 *************"
    echo "  $0 my-server.com ubuntu"
    echo "  $0 ************* deploy /opt/img-proc-tools"
    echo
    echo "前置要求:"
    echo "  1. 本地已安装 Node.js 16+"
    echo "  2. 服务器已安装 Node.js, PM2, Nginx"
    echo "  3. 已配置 SSH 密钥认证"
    echo
}

# 检查参数
check_parameters() {
    if [[ -z "$SERVER_HOST" ]]; then
        echo "错误: 请提供服务器地址"
        echo
        show_help
        exit 1
    fi
    
    info "目标服务器: $SERVER_USER@$SERVER_HOST"
    info "项目目录: $PROJECT_DIR"
    info "构建版本: $VERSION"
}

# 检查本地环境
check_local_environment() {
    step "检查本地环境"
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js 未安装，请先安装 Node.js 16+"
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt 16 ]]; then
        error "Node.js 版本过低，需要 v16.0.0 或更高版本"
    fi
    
    # 检查项目结构
    if [[ ! -f "package.json" ]] || [[ ! -d "client" ]] || [[ ! -d "server" ]]; then
        error "请在项目根目录运行此脚本"
    fi
    
    # 检查 SSH 连接
    if ! ssh -o ConnectTimeout=5 -o BatchMode=yes "$SERVER_USER@$SERVER_HOST" exit 2>/dev/null; then
        error "无法连接到服务器 $SERVER_USER@$SERVER_HOST，请检查 SSH 配置"
    fi
    
    info "本地环境检查通过"
}

# 本地构建
local_build() {
    step "本地构建项目"
    
    # 清理旧文件
    rm -rf deploy-package ${PROJECT_NAME}-*.tar.gz
    
    # 安装依赖
    info "安装依赖..."
    npm install
    cd client && npm install && cd ..
    cd server && npm install && cd ..
    
    # 构建前端
    info "构建前端..."
    cd client
    npm run build
    cd ..
    
    # 构建后端
    info "构建后端..."
    cd server
    npm run build
    cd ..
    
    # 创建部署包
    info "创建部署包..."
    mkdir -p deploy-package
    cp -r client/dist deploy-package/frontend
    mkdir -p deploy-package/backend
    cp -r server/dist deploy-package/backend/
    cp server/package.json deploy-package/backend/
    cp server/package-lock.json deploy-package/backend/ 2>/dev/null || true
    
    # 创建配置文件
    cat > deploy-package/ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '${PROJECT_NAME}-server',
    script: './backend/dist/index.js',
    instances: 1,
    exec_mode: 'fork',
    env: { NODE_ENV: 'production', PORT: 3011 },
    error_file: './backend/logs/err.log',
    out_file: './backend/logs/out.log',
    max_memory_restart: '1G',
    autorestart: true
  }]
};
EOF
    
    cat > deploy-package/backend/.env.example << EOF
PORT=3011
NODE_ENV=production
COMFYUI_API_URL=http://localhost:8188
COMFYUI_HOST=localhost
COMFYUI_PORT=8188
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
RESULTS_DIR=results
API_TIMEOUT=300000
EOF
    
    # 打包
    tar -czf $PACKAGE_NAME -C deploy-package .
    
    local package_size=$(du -sh $PACKAGE_NAME | cut -f1)
    info "本地构建完成 - 包大小: $package_size"
}

# 上传到服务器
upload_to_server() {
    step "上传部署包到服务器"
    
    # 上传部署包
    info "上传 $PACKAGE_NAME 到服务器..."
    scp "$PACKAGE_NAME" "$SERVER_USER@$SERVER_HOST:/tmp/"
    
    # 上传部署脚本
    if [[ -f "scripts/server-deploy.sh" ]]; then
        info "上传部署脚本..."
        scp "scripts/server-deploy.sh" "$SERVER_USER@$SERVER_HOST:/tmp/"
    fi
    
    info "文件上传完成"
}

# 服务器端部署
deploy_on_server() {
    step "在服务器上执行部署"
    
    # 执行部署命令
    ssh "$SERVER_USER@$SERVER_HOST" << EOF
set -e

echo "开始服务器端部署..."

# 检查环境
if ! command -v node &> /dev/null; then
    echo "错误: Node.js 未安装"
    exit 1
fi

if ! command -v pm2 &> /dev/null; then
    echo "错误: PM2 未安装，请先安装: sudo npm install -g pm2"
    exit 1
fi

# 备份现有部署
if [[ -d "$PROJECT_DIR" ]]; then
    echo "备份现有部署..."
    sudo cp -r "$PROJECT_DIR" "$PROJECT_DIR.backup.\$(date +%Y%m%d_%H%M%S)" || true
fi

# 停止现有服务
echo "停止现有服务..."
pm2 stop "${PROJECT_NAME}-server" 2>/dev/null || true
pm2 delete "${PROJECT_NAME}-server" 2>/dev/null || true

# 创建项目目录
sudo mkdir -p "$PROJECT_DIR"
sudo chown -R \$USER:\$USER "$PROJECT_DIR"

# 清空并解压
rm -rf "$PROJECT_DIR"/*
echo "解压部署包..."
tar -xzf "/tmp/$PACKAGE_NAME" -C "$PROJECT_DIR"

# 安装后端依赖
echo "安装后端依赖..."
cd "$PROJECT_DIR/backend"
npm ci --only=production

# 创建必要目录
mkdir -p uploads results logs
chmod 755 uploads results logs

# 配置环境变量
if [[ ! -f .env ]]; then
    cp .env.example .env
    echo "已创建环境变量文件，请根据需要编辑 .env"
fi

# 启动服务
echo "启动服务..."
cd "$PROJECT_DIR"
pm2 start ecosystem.config.js
pm2 save

echo "服务器端部署完成"
EOF
    
    info "服务器端部署完成"
}

# 健康检查
health_check() {
    step "执行健康检查"
    
    # 检查服务状态
    ssh "$SERVER_USER@$SERVER_HOST" << EOF
echo "检查服务状态..."

# 等待服务启动
sleep 3

# 检查 PM2 状态
pm2 list | grep "${PROJECT_NAME}-server" || echo "警告: PM2 服务未找到"

# 检查后端 API
if curl -f -s http://localhost:3011 >/dev/null 2>&1; then
    echo "✓ 后端 API 健康检查通过"
else
    echo "✗ 后端 API 健康检查失败"
    echo "请检查日志: pm2 logs ${PROJECT_NAME}-server"
fi

# 检查前端文件
if [[ -f "$PROJECT_DIR/frontend/index.html" ]]; then
    echo "✓ 前端文件检查通过"
else
    echo "✗ 前端文件检查失败"
fi
EOF
    
    info "健康检查完成"
}

# 显示部署结果
show_result() {
    step "部署完成"
    
    echo
    echo "=========================================="
    echo "  部署成功！"
    echo "=========================================="
    echo "服务器: $SERVER_USER@$SERVER_HOST"
    echo "项目目录: $PROJECT_DIR"
    echo "构建版本: $VERSION"
    echo "部署包: $PACKAGE_NAME"
    echo
    echo "服务管理:"
    echo "  ssh $SERVER_USER@$SERVER_HOST"
    echo "  pm2 status"
    echo "  pm2 logs ${PROJECT_NAME}-server"
    echo "  pm2 restart ${PROJECT_NAME}-server"
    echo
    echo "访问地址:"
    echo "  后端API: http://$SERVER_HOST:3011"
    echo "  前端页面: 需要配置 Nginx"
    echo
    echo "Nginx 配置提示:"
    echo "  root $PROJECT_DIR/frontend;"
    echo "  proxy_pass http://localhost:3011;"
    echo "=========================================="
    echo
    echo "🎉 部署完成！"
}

# 清理本地文件
cleanup() {
    step "清理本地临时文件"
    
    rm -rf deploy-package
    rm -f $PACKAGE_NAME
    
    info "清理完成"
}

# 主函数
main() {
    log "开始本地构建 + 服务器部署"
    
    check_parameters
    check_local_environment
    local_build
    upload_to_server
    deploy_on_server
    health_check
    show_result
    cleanup
    
    log "部署流程全部完成！"
}

# 错误处理
trap 'error "部署过程中发生错误"' ERR

# 执行主函数
main "$@"
