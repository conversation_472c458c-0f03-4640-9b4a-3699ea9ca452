#!/bin/bash

# 图像处理工具服务重启脚本
# 使用方法: ./restart.sh [服务名]

set -e

PROJECT_NAME="img-proc-tools"
LOG_FILE="/var/log/restart-$PROJECT_NAME.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
    exit 1
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a $LOG_FILE
}

# 重启后端服务
restart_backend() {
    log "重启后端服务..."
    
    if pm2 list | grep -q "$PROJECT_NAME-server"; then
        pm2 restart "$PROJECT_NAME-server"
        info "后端服务重启完成"
    else
        error "后端服务未找到，请先部署应用"
    fi
}

# 重载 Nginx
reload_nginx() {
    log "重载 Nginx 配置..."
    
    # 测试配置
    sudo nginx -t
    
    # 重载配置
    sudo systemctl reload nginx
    
    info "Nginx 重载完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    sleep 3
    
    # 检查后端
    if curl -f http://localhost:3011 >/dev/null 2>&1; then
        info "后端服务运行正常"
    else
        error "后端服务异常"
    fi
    
    # 检查前端
    if curl -f http://localhost >/dev/null 2>&1; then
        info "前端服务运行正常"
    else
        error "前端服务异常"
    fi
}

# 显示服务状态
show_status() {
    echo
    echo "=========================================="
    echo "  服务状态信息"
    echo "=========================================="
    
    # PM2 状态
    echo "PM2 进程状态:"
    pm2 list
    
    echo
    echo "Nginx 状态:"
    sudo systemctl status nginx --no-pager -l
    
    echo
    echo "端口占用情况:"
    sudo netstat -tlnp | grep -E ':(80|3011|443)\s'
    
    echo "=========================================="
}

# 主函数
main() {
    log "开始重启 $PROJECT_NAME 服务"
    
    restart_backend
    reload_nginx
    health_check
    show_status
    
    log "服务重启完成！"
}

# 错误处理
trap 'error "重启过程中发生错误"' ERR

# 执行主函数
main "$@"
